{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# UCA Caboolture Region - ItemisedReport-May Data Manipulation\n",
    "\n",
    "This notebook provides easy tools for manipulating data in the ItemisedReport-May.csv file.\n",
    "You can easily change cell values, ranges, and perform data analysis.\n",
    "\n",
    "## Features:\n",
    "- Load and display data in a clean table format\n",
    "- Edit individual cells or ranges\n",
    "- Add/remove rows and columns\n",
    "- Data validation and cleaning\n",
    "- Export cleaned data back to CSV\n",
    "- Generate summary statistics\n",
    "- Visualize transaction patterns"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Import required libraries\n",
    "import pandas as pd\n",
    "import numpy as np\n",
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "from datetime import datetime\n",
    "import warnings\n",
    "warnings.filterwarnings('ignore')\n",
    "\n",
    "# Set display options for better viewing\n",
    "pd.set_option('display.max_columns', None)\n",
    "pd.set_option('display.max_rows', 50)\n",
    "pd.set_option('display.width', None)\n",
    "pd.set_option('display.max_colwidth', 50)\n",
    "\n",
    "print(\"✅ Libraries loaded successfully!\")\n",
    "print(\"📊 Ready for data manipulation\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 1. Load the ItemisedReport-May Data"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Load the ItemisedReport-May.csv file\n",
    "file_path = 'Square_Reports/square - 5 may/ItemisedReport-May.csv'\n",
    "\n",
    "try:\n",
    "    # Load the data\n",
    "    df = pd.read_csv(file_path)\n",
    "    \n",
    "    print(f\"✅ Data loaded successfully!\")\n",
    "    print(f\"📊 Shape: {df.shape[0]} rows × {df.shape[1]} columns\")\n",
    "    print(f\"📅 Date range: {df['Date'].min()} to {df['Date'].max()}\")\n",
    "    \n",
    "    # Display first few rows\n",
    "    print(\"\\n🔍 First 5 rows:\")\n",
    "    display(df.head())\n",
    "    \n",
    "except FileNotFoundError:\n",
    "    print(\"❌ File not found. Please check the file path.\")\n",
    "except Exception as e:\n",
    "    print(f\"❌ Error loading file: {e}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2. Data Overview and Structure"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Display data info and structure\n",
    "print(\"📋 Data Information:\")\n",
    "print(df.info())\n",
    "\n",
    "print(\"\\n📊 Column Names:\")\n",
    "for i, col in enumerate(df.columns):\n",
    "    print(f\"{i}: {col}\")\n",
    "\n",
    "print(\"\\n📈 Data Types:\")\n",
    "display(df.dtypes)\n",
    "\n",
    "print(\"\\n🔢 Basic Statistics:\")\n",
    "display(df.describe())"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 3. Easy Cell and Range Editing Functions\n",
    "\n",
    "These functions make it easy to edit data:"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def edit_cell(row_index, column_name, new_value):\n",
    "    \"\"\"\n",
    "    Edit a single cell in the dataframe\n",
    "    \n",
    "    Parameters:\n",
    "    row_index: int - Row number (0-based)\n",
    "    column_name: str - Column name\n",
    "    new_value: any - New value for the cell\n",
    "    \"\"\"\n",
    "    try:\n",
    "        old_value = df.at[row_index, column_name]\n",
    "        df.at[row_index, column_name] = new_value\n",
    "        print(f\"✅ Cell [{row_index}, '{column_name}'] changed from '{old_value}' to '{new_value}'\")\n",
    "        return True\n",
    "    except Exception as e:\n",
    "        print(f\"❌ Error editing cell: {e}\")\n",
    "        return False\n",
    "\n",
    "def edit_range(row_start, row_end, column_name, new_value):\n",
    "    \"\"\"\n",
    "    Edit a range of cells in a column\n",
    "    \n",
    "    Parameters:\n",
    "    row_start: int - Starting row (inclusive)\n",
    "    row_end: int - Ending row (inclusive)\n",
    "    column_name: str - Column name\n",
    "    new_value: any - New value for all cells in range\n",
    "    \"\"\"\n",
    "    try:\n",
    "        df.loc[row_start:row_end, column_name] = new_value\n",
    "        print(f\"✅ Range [{row_start}:{row_end}, '{column_name}'] updated to '{new_value}'\")\n",
    "        return True\n",
    "    except Exception as e:\n",
    "        print(f\"❌ Error editing range: {e}\")\n",
    "        return False\n",
    "\n",
    "def view_rows(start_row, end_row=None):\n",
    "    \"\"\"\n",
    "    View specific rows\n",
    "    \n",
    "    Parameters:\n",
    "    start_row: int - Starting row\n",
    "    end_row: int - Ending row (optional, defaults to start_row)\n",
    "    \"\"\"\n",
    "    if end_row is None:\n",
    "        end_row = start_row\n",
    "    \n",
    "    try:\n",
    "        result = df.iloc[start_row:end_row+1]\n",
    "        print(f\"📋 Rows {start_row} to {end_row}:\")\n",
    "        display(result)\n",
    "        return result\n",
    "    except Exception as e:\n",
    "        print(f\"❌ Error viewing rows: {e}\")\n",
    "        return None\n",
    "\n",
    "def search_data(column_name, search_value):\n",
    "    \"\"\"\n",
    "    Search for specific values in a column\n",
    "    \n",
    "    Parameters:\n",
    "    column_name: str - Column to search in\n",
    "    search_value: any - Value to search for\n",
    "    \"\"\"\n",
    "    try:\n",
    "        if isinstance(search_value, str):\n",
    "            # Case-insensitive string search\n",
    "            mask = df[column_name].astype(str).str.contains(search_value, case=False, na=False)\n",
    "        else:\n",
    "            # Exact match for non-string values\n",
    "            mask = df[column_name] == search_value\n",
    "        \n",
    "        results = df[mask]\n",
    "        print(f\"🔍 Found {len(results)} matches for '{search_value}' in '{column_name}':\")\n",
    "        display(results)\n",
    "        return results\n",
    "    except Exception as e:\n",
    "        print(f\"❌ Error searching: {e}\")\n",
    "        return None\n",
    "\n",
    "print(\"✅ Editing functions loaded!\")\n",
    "print(\"\\n📝 Available functions:\")\n",
    "print(\"• edit_cell(row_index, column_name, new_value)\")\n",
    "print(\"• edit_range(row_start, row_end, column_name, new_value)\")\n",
    "print(\"• view_rows(start_row, end_row)\")\n",
    "print(\"• search_data(column_name, search_value)\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 4. Quick Data Exploration\n",
    "\n",
    "Let's explore the data to understand what we're working with:"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Remove empty rows (where Date is NaN or empty)\n",
    "df_clean = df.dropna(subset=['Date'])\n",
    "df_clean = df_clean[df_clean['Date'] != '']\n",
    "\n",
    "print(f\"📊 Clean data: {len(df_clean)} rows (removed {len(df) - len(df_clean)} empty rows)\")\n",
    "\n",
    "# Show unique transaction types\n",
    "print(\"\\n🏷️ Transaction Categories:\")\n",
    "if 'Category' in df_clean.columns:\n",
    "    category_counts = df_clean['Category'].value_counts()\n",
    "    display(category_counts)\n",
    "\n",
    "# Show payment methods\n",
    "print(\"\\n💳 Payment Methods:\")\n",
    "if 'Payment_Method' in df_clean.columns:\n",
    "    payment_counts = df_clean['Payment_Method'].value_counts()\n",
    "    display(payment_counts)\n",
    "\n",
    "# Show amount statistics\n",
    "print(\"\\n💰 Amount Statistics:\")\n",
    "if 'Amount' in df_clean.columns:\n",
    "    amount_stats = df_clean['Amount'].describe()\n",
    "    display(amount_stats)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 5. Example: Edit Specific Cells\n",
    "\n",
    "Here are some examples of how to edit data:"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Example 1: View a specific row before editing\n",
    "print(\"📋 Example: Viewing row 10\")\n",
    "view_rows(10)\n",
    "\n",
    "# Example 2: Search for offering transactions\n",
    "print(\"\\n🔍 Example: Searching for 'Offering' transactions\")\n",
    "offering_results = search_data('Description', 'Offering')\n",
    "\n",
    "# Example 3: Edit a cell (uncomment to use)\n",
    "# edit_cell(10, 'Description', 'Updated Description')\n",
    "\n",
    "# Example 4: Edit a range (uncomment to use)\n",
    "# edit_range(5, 10, 'Category', 'Updated Category')\n",
    "\n",
    "print(\"\\n💡 To edit data, uncomment the edit_cell() or edit_range() lines above and run the cell\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 6. Data Cleaning and Validation"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def clean_amounts():\n",
    "    \"\"\"\n",
    "    Clean amount columns by removing $ signs and converting to float\n",
    "    \"\"\"\n",
    "    amount_columns = ['Amount', 'Gross_Amount', 'Processing_Fees']\n",
    "    \n",
    "    for col in amount_columns:\n",
    "        if col in df.columns:\n",
    "            # Remove $ signs and convert to float\n",
    "            df[col] = df[col].astype(str).str.replace('$', '').str.replace(',', '')\n",
    "            df[col] = pd.to_numeric(df[col], errors='coerce')\n",
    "            print(f\"✅ Cleaned {col} column\")\n",
    "\n",
    "def validate_data():\n",
    "    \"\"\"\n",
    "    Validate data for common issues\n",
    "    \"\"\"\n",
    "    print(\"🔍 Data Validation Report:\")\n",
    "    \n",
    "    # Check for missing values\n",
    "    missing_data = df.isnull().sum()\n",
    "    if missing_data.sum() > 0:\n",
    "        print(\"\\n⚠️ Missing values found:\")\n",
    "        display(missing_data[missing_data > 0])\n",
    "    else:\n",
    "        print(\"✅ No missing values found\")\n",
    "    \n",
    "    # Check for duplicate rows\n",
    "    duplicates = df.duplicated().sum()\n",
    "    if duplicates > 0:\n",
    "        print(f\"\\n⚠️ Found {duplicates} duplicate rows\")\n",
    "    else:\n",
    "        print(\"✅ No duplicate rows found\")\n",
    "    \n",
    "    # Check for negative amounts (should only be in fees)\n",
    "    if 'Amount' in df.columns:\n",
    "        negative_amounts = (df['Amount'] < 0).sum()\n",
    "        if negative_amounts > 0:\n",
    "            print(f\"\\n⚠️ Found {negative_amounts} negative amounts\")\n",
    "        else:\n",
    "            print(\"✅ No negative amounts in main Amount column\")\n",
    "\n",
    "# Run validation\n",
    "validate_data()\n",
    "\n",
    "# Uncomment to clean amounts\n",
    "# clean_amounts()"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 7. Save Changes Back to CSV"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def save_data(filename=None, backup=True):\n",
    "    \"\"\"\n",
    "    Save the modified dataframe back to CSV\n",
    "    \n",
    "    Parameters:\n",
    "    filename: str - Optional custom filename\n",
    "    backup: bool - Whether to create a backup of original file\n",
    "    \"\"\"\n",
    "    try:\n",
    "        if filename is None:\n",
    "            filename = file_path\n",
    "        \n",
    "        # Create backup if requested\n",
    "        if backup and filename == file_path:\n",
    "            backup_name = file_path.replace('.csv', '_backup.csv')\n",
    "            import shutil\n",
    "            shutil.copy2(file_path, backup_name)\n",
    "            print(f\"📁 Backup created: {backup_name}\")\n",
    "        \n",
    "        # Save the data\n",
    "        df.to_csv(filename, index=False)\n",
    "        print(f\"✅ Data saved to: {filename}\")\n",
    "        print(f\"📊 Saved {len(df)} rows × {len(df.columns)} columns\")\n",
    "        \n",
    "    except Exception as e:\n",
    "        print(f\"❌ Error saving file: {e}\")\n",
    "\n",
    "# Example usage (uncomment to save):\n",
    "# save_data()  # Save to original file with backup\n",
    "# save_data('ItemisedReport-May-Modified.csv', backup=False)  # Save to new file\n",
    "\n",
    "print(\"💾 Save function ready!\")\n",
    "print(\"\\n📝 Usage:\")\n",
    "print(\"• save_data() - Save to original file with backup\")\n",
    "print(\"• save_data('new_filename.csv') - Save to new file\")\n",
    "print(\"• save_data(backup=False) - Save without creating backup\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 8. Data Visualization"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Create visualizations of the transaction data\n",
    "plt.style.use('default')\n",
    "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n",
    "fig.suptitle('UCA Caboolture Region - May 2025 Transaction Analysis', fontsize=16, fontweight='bold')\n",
    "\n",
    "# Clean data for plotting\n",
    "df_plot = df.dropna(subset=['Date'])\n",
    "df_plot = df_plot[df_plot['Date'] != '']\n",
    "\n",
    "if len(df_plot) > 0:\n",
    "    # 1. Transaction Categories\n",
    "    if 'Category' in df_plot.columns:\n",
    "        category_counts = df_plot['Category'].value_counts()\n",
    "        axes[0,0].pie(category_counts.values, labels=category_counts.index, autopct='%1.1f%%')\n",
    "        axes[0,0].set_title('Transaction Categories')\n",
    "    \n",
    "    # 2. Payment Methods\n",
    "    if 'Payment_Method' in df_plot.columns:\n",
    "        payment_counts = df_plot['Payment_Method'].value_counts()\n",
    "        axes[0,1].bar(payment_counts.index, payment_counts.values)\n",
    "        axes[0,1].set_title('Payment Methods')\n",
    "        axes[0,1].tick_params(axis='x', rotation=45)\n",
    "    \n",
    "    # 3. Amount Distribution\n",
    "    if 'Amount' in df_plot.columns:\n",
    "        # Convert amounts to numeric, handling any string values\n",
    "        amounts = pd.to_numeric(df_plot['Amount'], errors='coerce')\n",
    "        amounts = amounts.dropna()\n",
    "        if len(amounts) > 0:\n",
    "            axes[1,0].hist(amounts, bins=20, edgecolor='black', alpha=0.7)\n",
    "            axes[1,0].set_title('Amount Distribution')\n",
    "            axes[1,0].set_xlabel('Amount ($)')\n",
    "            axes[1,0].set_ylabel('Frequency')\n",
    "    \n",
    "    # 4. Daily Transaction Count\n",
    "    if 'Date' in df_plot.columns:\n",
    "        try:\n",
    "            df_plot['Date'] = pd.to_datetime(df_plot['Date'])\n",
    "            daily_counts = df_plot['Date'].dt.date.value_counts().sort_index()\n",
    "            axes[1,1].plot(daily_counts.index, daily_counts.values, marker='o')\n",
    "            axes[1,1].set_title('Daily Transaction Count')\n",
    "            axes[1,1].set_xlabel('Date')\n",
    "            axes[1,1].set_ylabel('Number of Transactions')\n",
    "            axes[1,1].tick_params(axis='x', rotation=45)\n",
    "        except:\n",
    "            axes[1,1].text(0.5, 0.5, 'Date format issue', ha='center', va='center')\n",
    "            axes[1,1].set_title('Daily Transaction Count')\n",
    "\nplt.tight_layout()\nplt.show()\n",
    "\nprint(\"📊 Visualizations created!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 9. Highlight Missing Documentation Transactions

Based on the feedback report, let's identify and highlight transactions that need documentation:\n",
    "\n",
    "### Common Tasks:\n",
    "\n",
    "**View data:**\n",
    "```python
\n",
    "df.head()  # First 5 rows\n",
    "df.tail()  # Last 5 rows\n",
    "view_rows(10, 15)  # Rows 10-15\n",
    "
```\n",
    "\n",
    "**Edit single cell:**\n",
    "```python
\n",
    "edit_cell(row_index, 'column_name', 'new_value')\n",
    "
```\n",
    "\n",
    "**Edit range:**\n",
    "```python
\n",
    "edit_range(start_row, end_row, 'column_name', 'new_value')\n",
    "
```\n",
    "\n",
    "**Search data:**\n",
    "```python
\n",
    "search_data('Description', 'Offering')\n",
    "search_data('Amount', 200.0)\n",
    "
```\n",
    "\n",
    "**Save changes:**\n",
    "```python
\n",
    "save_data()  # Save with backup\n",
    "save_data('new_filename.csv')  # Save to new file\n",
    "
```"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.12.10"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
