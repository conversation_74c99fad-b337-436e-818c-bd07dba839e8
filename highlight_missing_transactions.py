#!/usr/bin/env python3
"""
Highlight Missing Documentation Transactions
Based on feedback report from data entry person
"""

import pandas as pd
import numpy as np
from datetime import datetime

def load_data():
    """Load the Square transaction summary CSV file"""
    file_path = 'Square_Reports/square - 5 may/square transaction summary for printing.csv'
    df = pd.read_csv(file_path)
    return df

def identify_missing_transactions():
    """
    Identify transactions mentioned in the feedback report that need documentation
    """
    
    # Amounts mentioned in the feedback report that need documentation
    missing_amounts = [
        45.00,    # Square deposit slip needed
        381.00,   # Square deposit slip needed  
        200.00,   # Square deposit slip needed (C Pipemartin offering)
        105.00,   # Square deposit slip needed (mentioned only $100 supplied)
        7.00,     # Square deposit slip needed
        881.50,   # 26/5 Square deposit slip needed
        255.00,   # 26/05 Square deposit slip needed
        98.00,    # 02/06 Square deposit slip needed
        577.88,   # 09/05 All Pay slip needed
        578.39,   # 26/05 All Pay slip needed
        9542.82,  # 27/05 All Pay slip needed
        27.00,    # 15/05 bookkeeping invoice
        1467.00,  # 19/05 CPMP invoice
        300.00,   # 27/05 Corrected docket
        6259.00,  # 28/05 excavator documentation
        99.00,    # 30/05 Halaxy documentation
        495.00,   # 04/06 documentation needed
        150.00,   # 04/06 documentation needed
        122.00,   # 30/05 CCA donation documentation
        275.00,   # 31/05 Scripture Union payment
    ]
    
    # Special cases with dates
    special_cases = [
        {'date': '2025-05-09', 'description': 'Lay Preacher', 'note': 'Email/voucher for lay preacher payment at Beachmere'},
        {'date': '2025-05-25', 'amount': 200.00, 'customer': 'C Pipemartin', 'note': 'Square deposit slip needed'},
        {'date': '2025-05-24', 'amount': 881.50, 'note': '26/5 Square deposit slip needed (total for day)'},
    ]
    
    return missing_amounts, special_cases

def highlight_transactions(df):
    """
    Add a 'NEEDS_DOCUMENTATION' column to highlight problematic transactions
    """
    missing_amounts, special_cases = identify_missing_transactions()
    
    # Create a copy of the dataframe
    df_highlighted = df.copy()
    
    # Add highlighting column
    df_highlighted['NEEDS_DOCUMENTATION'] = ''
    df_highlighted['DOCUMENTATION_NOTE'] = ''
    
    # Clean data for processing
    df_clean = df_highlighted.dropna(subset=['Date'])
    df_clean = df_clean[df_clean['Date'] != '']
    
    print("🔍 Identifying transactions that need documentation...")
    
    highlighted_count = 0
    
    # Check each transaction
    for idx, row in df_clean.iterrows():
        # Clean amount by removing $ and converting to float
        amount_str = str(row['Net Sale']).replace('$', '').replace(',', '') if pd.notna(row['Net Sale']) else '0'
        try:
            amount = float(amount_str)
        except ValueError:
            amount = 0
        date = str(row['Date'])
        description = ''  # No description column in this CSV
        customer = str(row['Customer Name']) if pd.notna(row['Customer Name']) else ''
        
        # Check for specific amounts mentioned in feedback
        if amount in missing_amounts:
            df_highlighted.at[idx, 'NEEDS_DOCUMENTATION'] = '⚠️ YES'
            
            # Add specific notes based on amount
            if amount == 200.00 and 'Offering' in description:
                df_highlighted.at[idx, 'DOCUMENTATION_NOTE'] = 'Square deposit slip needed (mentioned in feedback)'
            elif amount == 881.50:
                df_highlighted.at[idx, 'DOCUMENTATION_NOTE'] = '26/5 Square deposit slip needed (daily total)'
            elif amount in [45.00, 381.00, 105.00, 7.00, 255.00, 98.00]:
                df_highlighted.at[idx, 'DOCUMENTATION_NOTE'] = 'Square deposit slip required'
            elif amount in [577.88, 578.39, 9542.82]:
                df_highlighted.at[idx, 'DOCUMENTATION_NOTE'] = 'All Pay slip required'
            elif amount == 27.00:
                df_highlighted.at[idx, 'DOCUMENTATION_NOTE'] = 'Bookkeeping invoice documentation needed'
            elif amount == 1467.00:
                df_highlighted.at[idx, 'DOCUMENTATION_NOTE'] = 'CPMP invoice documentation needed'
            elif amount == 300.00:
                df_highlighted.at[idx, 'DOCUMENTATION_NOTE'] = 'Corrected docket needed'
            elif amount == 6259.00:
                df_highlighted.at[idx, 'DOCUMENTATION_NOTE'] = 'Excavator documentation needed'
            elif amount == 99.00:
                df_highlighted.at[idx, 'DOCUMENTATION_NOTE'] = 'Halaxy documentation needed'
            elif amount in [495.00, 150.00]:
                df_highlighted.at[idx, 'DOCUMENTATION_NOTE'] = 'Payment documentation needed'
            elif amount == 122.00:
                df_highlighted.at[idx, 'DOCUMENTATION_NOTE'] = 'CCA donation documentation needed'
            elif amount == 275.00:
                df_highlighted.at[idx, 'DOCUMENTATION_NOTE'] = 'Scripture Union payment documentation needed'
            
            highlighted_count += 1
        
        # Check for special cases
        for case in special_cases:
            if 'date' in case and case['date'] in date:
                # For now, skip description-based matching since we don't have descriptions
                # This would need to be matched by other criteria like amount or customer
                pass
        
        # Check for specific customer cases
        if 'C Pipemartin' in customer and amount == 200.00:
            df_highlighted.at[idx, 'NEEDS_DOCUMENTATION'] = '⚠️ YES'
            df_highlighted.at[idx, 'DOCUMENTATION_NOTE'] = 'Square deposit slip needed (C Pipemartin offering)'
            highlighted_count += 1

        # Check for Dwight Walker offerings
        if 'Dwight Walker' in customer and amount in [5.00, 7.00]:
            df_highlighted.at[idx, 'NEEDS_DOCUMENTATION'] = '⚠️ YES'
            df_highlighted.at[idx, 'DOCUMENTATION_NOTE'] = 'Square deposit slip needed (Dwight Walker offering)'
            highlighted_count += 1
    
    print(f"✅ Highlighted {highlighted_count} transactions that need documentation")
    return df_highlighted

def create_summary_report(df_highlighted):
    """
    Create a summary of all highlighted transactions
    """
    # Filter only highlighted transactions
    highlighted_df = df_highlighted[df_highlighted['NEEDS_DOCUMENTATION'] == '⚠️ YES'].copy()
    
    if len(highlighted_df) == 0:
        print("No transactions found matching the feedback criteria")
        return None
    
    # Clean up the display
    summary_df = highlighted_df[['Date', 'Time', 'Net Sale', 'Card Brand', 'Customer Name',
                                'DOCUMENTATION_NOTE']].copy()
    
    # Sort by date
    summary_df = summary_df.sort_values('Date')
    
    print(f"\n📋 SUMMARY: {len(summary_df)} transactions need documentation")
    print("=" * 80)
    
    for idx, row in summary_df.iterrows():
        # Clean amount for display
        amount_str = str(row['Net Sale']).replace('$', '').replace(',', '')
        try:
            amount_float = float(amount_str)
            amount_display = f"${amount_float:.2f}"
        except ValueError:
            amount_display = str(row['Net Sale'])

        print(f"Date: {row['Date']} | Amount: {amount_display} | {row['DOCUMENTATION_NOTE']}")
        if pd.notna(row['Customer Name']) and row['Customer Name'] != '':
            print(f"  Customer: {row['Customer Name']}")
        if pd.notna(row['Card Brand']) and row['Card Brand'] != '':
            print(f"  Payment Method: {row['Card Brand']}")
        print("-" * 40)
    
    return summary_df

def save_highlighted_csv(df_highlighted):
    """
    Save the highlighted data to a new CSV file
    """
    output_file = 'ItemisedReport-May-HIGHLIGHTED.csv'
    df_highlighted.to_csv(output_file, index=False)
    print(f"\n💾 Highlighted data saved to: {output_file}")
    print("📝 Look for '⚠️ YES' in the NEEDS_DOCUMENTATION column")
    return output_file

def main():
    """
    Main function to process and highlight missing documentation
    """
    print("🔍 MISSING DOCUMENTATION HIGHLIGHTER")
    print("=" * 50)
    print("Based on feedback report from data entry person\n")
    
    try:
        # Load data
        df = load_data()
        print(f"📊 Loaded {len(df)} rows from ItemisedReport-May.csv")
        
        # Highlight transactions
        df_highlighted = highlight_transactions(df)
        
        # Create summary report
        summary_df = create_summary_report(df_highlighted)
        
        # Save highlighted CSV
        output_file = save_highlighted_csv(df_highlighted)
        
        print(f"\n✅ COMPLETE!")
        print(f"📁 Check {output_file} for highlighted transactions")
        print("🔍 Transactions marked with '⚠️ YES' need documentation")
        
        return df_highlighted, summary_df
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None, None

if __name__ == "__main__":
    df_highlighted, summary_df = main()
