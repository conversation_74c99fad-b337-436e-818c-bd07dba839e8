#!/usr/bin/env python3
"""
Create a visual highlighting report for missing documentation transactions
This creates an Excel file with color-coded highlighting for easy visual identification
"""

import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import PatternFill, Font, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows

def create_visual_report():
    """Create an Excel file with visual highlighting for missing documentation"""
    
    # Load the highlighted CSV
    df = pd.read_csv('ItemisedReport-May-HIGHLIGHTED.csv')
    
    # Create a new workbook
    wb = Workbook()
    ws = wb.active
    ws.title = "Missing Documentation Report"
    
    # Define styles
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_font = Font(color="FFFFFF", bold=True)
    
    highlight_fill = PatternFill(start_color="FFD700", end_color="FFD700", fill_type="solid")  # Gold
    highlight_font = Font(color="000000", bold=True)
    
    normal_fill = PatternFill(start_color="FFFFFF", end_color="FFFFFF", fill_type="solid")
    normal_font = Font(color="000000")
    
    # Add data to worksheet
    for r in dataframe_to_rows(df, index=False, header=True):
        ws.append(r)
    
    # Style the header row
    for cell in ws[1]:
        cell.fill = header_fill
        cell.font = header_font
        cell.alignment = Alignment(horizontal="center", vertical="center")
    
    # Style the data rows
    for row_num in range(2, ws.max_row + 1):
        needs_doc_cell = ws[f'S{row_num}']  # Column S is NEEDS_DOCUMENTATION
        
        if needs_doc_cell.value == '⚠️ YES':
            # Highlight the entire row
            for col_num in range(1, ws.max_column + 1):
                cell = ws.cell(row=row_num, column=col_num)
                cell.fill = highlight_fill
                if col_num in [19, 20]:  # NEEDS_DOCUMENTATION and DOCUMENTATION_NOTE columns
                    cell.font = highlight_font
        else:
            # Normal styling
            for col_num in range(1, ws.max_column + 1):
                cell = ws.cell(row=row_num, column=col_num)
                cell.fill = normal_fill
                cell.font = normal_font
    
    # Auto-adjust column widths
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        
        adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
        ws.column_dimensions[column_letter].width = adjusted_width
    
    # Add a summary sheet
    summary_ws = wb.create_sheet("Summary")
    
    # Count highlighted transactions
    highlighted_count = len(df[df['NEEDS_DOCUMENTATION'] == '⚠️ YES'])
    total_count = len(df)
    
    summary_data = [
        ["MISSING DOCUMENTATION SUMMARY", ""],
        ["", ""],
        ["Total Transactions:", total_count],
        ["Transactions Needing Documentation:", highlighted_count],
        ["Percentage Needing Documentation:", f"{(highlighted_count/total_count)*100:.1f}%"],
        ["", ""],
        ["BREAKDOWN BY DOCUMENTATION TYPE:", ""],
        ["", ""],
    ]
    
    # Add breakdown by documentation type
    doc_types = df[df['NEEDS_DOCUMENTATION'] == '⚠️ YES']['DOCUMENTATION_NOTE'].value_counts()
    for doc_type, count in doc_types.items():
        summary_data.append([doc_type, count])
    
    # Add summary data to worksheet
    for row_data in summary_data:
        summary_ws.append(row_data)
    
    # Style the summary sheet
    summary_ws['A1'].font = Font(size=16, bold=True, color="366092")
    summary_ws['A7'].font = Font(size=12, bold=True, color="366092")
    
    # Auto-adjust summary column widths
    for column in summary_ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        
        adjusted_width = max_length + 2
        summary_ws.column_dimensions[column_letter].width = adjusted_width
    
    # Save the workbook
    filename = 'Missing_Documentation_Report_HIGHLIGHTED.xlsx'
    wb.save(filename)
    
    print(f"✅ Visual highlighting report created: {filename}")
    print(f"📊 {highlighted_count} out of {total_count} transactions need documentation")
    print(f"🎨 Highlighted rows are shown in GOLD color")
    print(f"📋 Summary sheet included with breakdown by documentation type")
    
    return filename

def create_simple_csv_for_viewing():
    """Create a simplified CSV with only the highlighted transactions for easy viewing"""
    
    # Load the highlighted CSV
    df = pd.read_csv('ItemisedReport-May-HIGHLIGHTED.csv')
    
    # Filter only highlighted transactions
    highlighted_df = df[df['NEEDS_DOCUMENTATION'] == '⚠️ YES'].copy()
    
    # Select only the most important columns for viewing
    simple_df = highlighted_df[['Date', 'Time', 'Net Sale', 'Card Brand', 'Customer Name', 
                               'Transaction ID', 'DOCUMENTATION_NOTE']].copy()
    
    # Rename columns for clarity
    simple_df.columns = ['Date', 'Time', 'Amount', 'Payment_Method', 'Customer', 
                        'Transaction_ID', 'Documentation_Required']
    
    # Sort by date
    simple_df = simple_df.sort_values('Date')
    
    # Save to CSV
    filename = 'MISSING_DOCUMENTATION_ONLY.csv'
    simple_df.to_csv(filename, index=False)
    
    print(f"✅ Simplified report created: {filename}")
    print(f"📝 Contains only the {len(simple_df)} transactions that need documentation")
    
    return filename

def main():
    """Main function to create visual reports"""
    print("🎨 CREATING VISUAL HIGHLIGHTING REPORTS")
    print("=" * 50)
    
    try:
        # Create Excel file with visual highlighting
        excel_file = create_visual_report()
        
        print()
        
        # Create simplified CSV
        csv_file = create_simple_csv_for_viewing()
        
        print()
        print("📁 FILES CREATED:")
        print(f"1. {excel_file} - Full report with visual highlighting")
        print(f"2. {csv_file} - Simplified view of missing documentation only")
        print(f"3. ItemisedReport-May-HIGHLIGHTED.csv - Original with highlighting columns")
        
        print()
        print("💡 HOW TO USE:")
        print("• Open the Excel file to see highlighted rows in GOLD")
        print("• Use the simplified CSV for quick review")
        print("• Look for '⚠️ YES' in the NEEDS_DOCUMENTATION column")
        print("• Check the DOCUMENTATION_NOTE column for specific requirements")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
