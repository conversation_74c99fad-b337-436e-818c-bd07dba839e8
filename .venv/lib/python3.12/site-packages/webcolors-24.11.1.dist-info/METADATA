Metadata-Version: 2.1
Name: webcolors
Version: 24.11.1
Summary: A library for working with the color formats defined by HTML and CSS.
Keywords: color,css,html,web
Author: <PERSON>
License: BSD-3-Clause
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Utilities
Project-URL: Documentation, https://webcolors.readthedocs.io
Project-URL: Source Code, https://github.com/ubernostrum/webcolors
Requires-Python: >=3.9
Description-Content-Type: text/x-rst

.. -*-restructuredtext-*-

.. image:: https://github.com/ubernostrum/webcolors/workflows/CI/badge.svg
   :alt: CI status image
   :target: https://github.com/ubernostrum/webcolors/actions?query=workflow%3ACI

``webcolors`` is a module for working with and converting between the
various HTML/CSS color formats.

Support is included for normalizing and converting between the
following formats (RGB colorspace only; conversion to/from HSL can be
handled by the ``colorsys`` module in the Python standard library):

* Specification-defined color names

* Six-digit hexadecimal

* Three-digit hexadecimal

* Integer ``rgb()`` triplet

* Percentage ``rgb()`` triplet

For example:

.. code-block:: python

    >>> import webcolors
    >>> webcolors.hex_to_name("#daa520")
    'goldenrod'

Implementations are also provided for the HTML5 color parsing and
serialization algorithms. For example, parsing the infamous
"chucknorris" string into an ``rgb()`` triplet:

.. code-block:: python

    >>> import webcolors
    >>> webcolors.html5_parse_legacy_color("chucknorris")
    HTML5SimpleColor(red=192, green=0, blue=0)

Full documentation is `available online <https://webcolors.readthedocs.io/>`_.
