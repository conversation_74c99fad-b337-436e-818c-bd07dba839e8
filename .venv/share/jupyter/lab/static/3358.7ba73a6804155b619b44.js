"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[3358],{33358:(t,e,n)=>{n.d(e,{diagram:()=>pt});var i=n(75905);var r=n(24982);function s(t,e){let n;if(e===undefined){for(const e of t){if(e!=null&&(n>e||n===undefined&&e>=e)){n=e}}}else{let i=-1;for(let r of t){if((r=e(r,++i,t))!=null&&(n>r||n===undefined&&r>=r)){n=r}}}return n}function o(t){return t.target.depth}function a(t){return t.depth}function l(t,e){return e-1-t.height}function c(t,e){return t.sourceLinks.length?t.depth:e-1}function h(t){return t.targetLinks.length?t.depth:t.sourceLinks.length?s(t.sourceLinks,o)-1:0}function u(t,e){let n=0;if(e===undefined){for(let e of t){if(e=+e){n+=e}}}else{let i=-1;for(let r of t){if(r=+e(r,++i,t)){n+=r}}}return n}function f(t,e){let n;if(e===undefined){for(const e of t){if(e!=null&&(n<e||n===undefined&&e>=e)){n=e}}}else{let i=-1;for(let r of t){if((r=e(r,++i,t))!=null&&(n<r||n===undefined&&r>=r)){n=r}}}return n}function y(t){return function(){return t}}function d(t,e){return g(t.source,e.source)||t.index-e.index}function p(t,e){return g(t.target,e.target)||t.index-e.index}function g(t,e){return t.y0-e.y0}function _(t){return t.value}function k(t){return t.index}function x(t){return t.nodes}function m(t){return t.links}function v(t,e){const n=t.get(e);if(!n)throw new Error("missing: "+e);return n}function b({nodes:t}){for(const e of t){let t=e.y0;let n=t;for(const i of e.sourceLinks){i.y0=t+i.width/2;t+=i.width}for(const i of e.targetLinks){i.y1=n+i.width/2;n+=i.width}}}function w(){let t=0,e=0,n=1,i=1;let r=24;let o=8,a;let l=k;let h=c;let w;let L;let S=x;let E=m;let K=6;function A(){const t={nodes:S.apply(null,arguments),links:E.apply(null,arguments)};M(t);I(t);T(t);C(t);P(t);b(t);return t}A.update=function(t){b(t);return t};A.nodeId=function(t){return arguments.length?(l=typeof t==="function"?t:y(t),A):l};A.nodeAlign=function(t){return arguments.length?(h=typeof t==="function"?t:y(t),A):h};A.nodeSort=function(t){return arguments.length?(w=t,A):w};A.nodeWidth=function(t){return arguments.length?(r=+t,A):r};A.nodePadding=function(t){return arguments.length?(o=a=+t,A):o};A.nodes=function(t){return arguments.length?(S=typeof t==="function"?t:y(t),A):S};A.links=function(t){return arguments.length?(E=typeof t==="function"?t:y(t),A):E};A.linkSort=function(t){return arguments.length?(L=t,A):L};A.size=function(r){return arguments.length?(t=e=0,n=+r[0],i=+r[1],A):[n-t,i-e]};A.extent=function(r){return arguments.length?(t=+r[0][0],n=+r[1][0],e=+r[0][1],i=+r[1][1],A):[[t,e],[n,i]]};A.iterations=function(t){return arguments.length?(K=+t,A):K};function M({nodes:t,links:e}){for(const[i,r]of t.entries()){r.index=i;r.sourceLinks=[];r.targetLinks=[]}const n=new Map(t.map(((e,n)=>[l(e,n,t),e])));for(const[i,r]of e.entries()){r.index=i;let{source:t,target:e}=r;if(typeof t!=="object")t=r.source=v(n,t);if(typeof e!=="object")e=r.target=v(n,e);t.sourceLinks.push(r);e.targetLinks.push(r)}if(L!=null){for(const{sourceLinks:e,targetLinks:n}of t){e.sort(L);n.sort(L)}}}function I({nodes:t}){for(const e of t){e.value=e.fixedValue===undefined?Math.max(u(e.sourceLinks,_),u(e.targetLinks,_)):e.fixedValue}}function T({nodes:t}){const e=t.length;let n=new Set(t);let i=new Set;let r=0;while(n.size){for(const t of n){t.depth=r;for(const{target:e}of t.sourceLinks){i.add(e)}}if(++r>e)throw new Error("circular link");n=i;i=new Set}}function C({nodes:t}){const e=t.length;let n=new Set(t);let i=new Set;let r=0;while(n.size){for(const t of n){t.height=r;for(const{source:e}of t.targetLinks){i.add(e)}}if(++r>e)throw new Error("circular link");n=i;i=new Set}}function D({nodes:e}){const i=f(e,(t=>t.depth))+1;const s=(n-t-r)/(i-1);const o=new Array(i);for(const n of e){const e=Math.max(0,Math.min(i-1,Math.floor(h.call(null,n,i))));n.layer=e;n.x0=t+e*s;n.x1=n.x0+r;if(o[e])o[e].push(n);else o[e]=[n]}if(w)for(const t of o){t.sort(w)}return o}function N(t){const n=s(t,(t=>(i-e-(t.length-1)*a)/u(t,_)));for(const r of t){let t=e;for(const e of r){e.y0=t;e.y1=t+e.value*n;t=e.y1+a;for(const t of e.sourceLinks){t.width=t.value*n}}t=(i-t+a)/(r.length+1);for(let e=0;e<r.length;++e){const n=r[e];n.y0+=t*(e+1);n.y1+=t*(e+1)}R(r)}}function P(t){const n=D(t);a=Math.min(o,(i-e)/(f(n,(t=>t.length))-1));N(n);for(let e=0;e<K;++e){const t=Math.pow(.99,e);const i=Math.max(1-t,(e+1)/K);$(n,t,i);O(n,t,i)}}function O(t,e,n){for(let i=1,r=t.length;i<r;++i){const r=t[i];for(const t of r){let n=0;let i=0;for(const{source:e,value:s}of t.targetLinks){let r=s*(t.layer-e.layer);n+=W(e,t)*r;i+=r}if(!(i>0))continue;let r=(n/i-t.y0)*e;t.y0+=r;t.y1+=r;F(t)}if(w===undefined)r.sort(g);j(r,n)}}function $(t,e,n){for(let i=t.length,r=i-2;r>=0;--r){const i=t[r];for(const t of i){let n=0;let i=0;for(const{target:e,value:s}of t.sourceLinks){let r=s*(e.layer-t.layer);n+=G(t,e)*r;i+=r}if(!(i>0))continue;let r=(n/i-t.y0)*e;t.y0+=r;t.y1+=r;F(t)}if(w===undefined)i.sort(g);j(i,n)}}function j(t,n){const r=t.length>>1;const s=t[r];U(t,s.y0-a,r-1,n);z(t,s.y1+a,r+1,n);U(t,i,t.length-1,n);z(t,e,0,n)}function z(t,e,n,i){for(;n<t.length;++n){const r=t[n];const s=(e-r.y0)*i;if(s>1e-6)r.y0+=s,r.y1+=s;e=r.y1+a}}function U(t,e,n,i){for(;n>=0;--n){const r=t[n];const s=(r.y1-e)*i;if(s>1e-6)r.y0-=s,r.y1-=s;e=r.y0-a}}function F({sourceLinks:t,targetLinks:e}){if(L===undefined){for(const{source:{sourceLinks:t}}of e){t.sort(p)}for(const{target:{targetLinks:e}}of t){e.sort(d)}}}function R(t){if(L===undefined){for(const{sourceLinks:e,targetLinks:n}of t){e.sort(p);n.sort(d)}}}function W(t,e){let n=t.y0-(t.sourceLinks.length-1)*a/2;for(const{target:i,width:r}of t.sourceLinks){if(i===e)break;n+=r+a}for(const{source:i,width:r}of e.targetLinks){if(i===t)break;n-=r}return n}function G(t,e){let n=e.y0-(e.targetLinks.length-1)*a/2;for(const{source:i,width:r}of e.targetLinks){if(i===t)break;n+=r+a}for(const{target:i,width:r}of t.sourceLinks){if(i===e)break;n-=r}return n}return A}var L=Math.PI,S=2*L,E=1e-6,K=S-E;function A(){this._x0=this._y0=this._x1=this._y1=null;this._=""}function M(){return new A}A.prototype=M.prototype={constructor:A,moveTo:function(t,e){this._+="M"+(this._x0=this._x1=+t)+","+(this._y0=this._y1=+e)},closePath:function(){if(this._x1!==null){this._x1=this._x0,this._y1=this._y0;this._+="Z"}},lineTo:function(t,e){this._+="L"+(this._x1=+t)+","+(this._y1=+e)},quadraticCurveTo:function(t,e,n,i){this._+="Q"+ +t+","+ +e+","+(this._x1=+n)+","+(this._y1=+i)},bezierCurveTo:function(t,e,n,i,r,s){this._+="C"+ +t+","+ +e+","+ +n+","+ +i+","+(this._x1=+r)+","+(this._y1=+s)},arcTo:function(t,e,n,i,r){t=+t,e=+e,n=+n,i=+i,r=+r;var s=this._x1,o=this._y1,a=n-t,l=i-e,c=s-t,h=o-e,u=c*c+h*h;if(r<0)throw new Error("negative radius: "+r);if(this._x1===null){this._+="M"+(this._x1=t)+","+(this._y1=e)}else if(!(u>E));else if(!(Math.abs(h*a-l*c)>E)||!r){this._+="L"+(this._x1=t)+","+(this._y1=e)}else{var f=n-s,y=i-o,d=a*a+l*l,p=f*f+y*y,g=Math.sqrt(d),_=Math.sqrt(u),k=r*Math.tan((L-Math.acos((d+u-p)/(2*g*_)))/2),x=k/_,m=k/g;if(Math.abs(x-1)>E){this._+="L"+(t+x*c)+","+(e+x*h)}this._+="A"+r+","+r+",0,0,"+ +(h*f>c*y)+","+(this._x1=t+m*a)+","+(this._y1=e+m*l)}},arc:function(t,e,n,i,r,s){t=+t,e=+e,n=+n,s=!!s;var o=n*Math.cos(i),a=n*Math.sin(i),l=t+o,c=e+a,h=1^s,u=s?i-r:r-i;if(n<0)throw new Error("negative radius: "+n);if(this._x1===null){this._+="M"+l+","+c}else if(Math.abs(this._x1-l)>E||Math.abs(this._y1-c)>E){this._+="L"+l+","+c}if(!n)return;if(u<0)u=u%S+S;if(u>K){this._+="A"+n+","+n+",0,1,"+h+","+(t-o)+","+(e-a)+"A"+n+","+n+",0,1,"+h+","+(this._x1=l)+","+(this._y1=c)}else if(u>E){this._+="A"+n+","+n+",0,"+ +(u>=L)+","+h+","+(this._x1=t+n*Math.cos(r))+","+(this._y1=e+n*Math.sin(r))}},rect:function(t,e,n,i){this._+="M"+(this._x0=this._x1=+t)+","+(this._y0=this._y1=+e)+"h"+ +n+"v"+ +i+"h"+-n+"Z"},toString:function(){return this._}};const I=M;var T=Array.prototype.slice;function C(t){return function e(){return t}}function D(t){return t[0]}function N(t){return t[1]}function P(t){return t.source}function O(t){return t.target}function $(t){var e=P,n=O,i=D,r=N,s=null;function o(){var o,a=T.call(arguments),l=e.apply(this,a),c=n.apply(this,a);if(!s)s=o=I();t(s,+i.apply(this,(a[0]=l,a)),+r.apply(this,a),+i.apply(this,(a[0]=c,a)),+r.apply(this,a));if(o)return s=null,o+""||null}o.source=function(t){return arguments.length?(e=t,o):e};o.target=function(t){return arguments.length?(n=t,o):n};o.x=function(t){return arguments.length?(i=typeof t==="function"?t:C(+t),o):i};o.y=function(t){return arguments.length?(r=typeof t==="function"?t:C(+t),o):r};o.context=function(t){return arguments.length?(s=t==null?null:t,o):s};return o}function j(t,e,n,i,r){t.moveTo(e,n);t.bezierCurveTo(e=(e+i)/2,n,e,r,i,r)}function z(t,e,n,i,r){t.moveTo(e,n);t.bezierCurveTo(e,n=(n+r)/2,i,n,i,r)}function U(t,e,n,i,r){var s=pointRadial(e,n),o=pointRadial(e,n=(n+r)/2),a=pointRadial(i,n),l=pointRadial(i,r);t.moveTo(s[0],s[1]);t.bezierCurveTo(o[0],o[1],a[0],a[1],l[0],l[1])}function F(){return $(j)}function R(){return $(z)}function W(){var t=$(U);t.angle=t.x,delete t.x;t.radius=t.y,delete t.y;return t}function G(t){return[t.source.x1,t.y0]}function V(t){return[t.target.x0,t.y1]}function X(){return F().source(G).target(V)}var Y=function(){var t=(0,i.K2)((function(t,e,n,i){for(n=n||{},i=t.length;i--;n[t[i]]=e);return n}),"o"),e=[1,9],n=[1,10],r=[1,5,10,12];var s={trace:(0,i.K2)((function t(){}),"trace"),yy:{},symbols_:{error:2,start:3,SANKEY:4,NEWLINE:5,csv:6,opt_eof:7,record:8,csv_tail:9,EOF:10,"field[source]":11,COMMA:12,"field[target]":13,"field[value]":14,field:15,escaped:16,non_escaped:17,DQUOTE:18,ESCAPED_TEXT:19,NON_ESCAPED_TEXT:20,$accept:0,$end:1},terminals_:{2:"error",4:"SANKEY",5:"NEWLINE",10:"EOF",11:"field[source]",12:"COMMA",13:"field[target]",14:"field[value]",18:"DQUOTE",19:"ESCAPED_TEXT",20:"NON_ESCAPED_TEXT"},productions_:[0,[3,4],[6,2],[9,2],[9,0],[7,1],[7,0],[8,5],[15,1],[15,1],[16,3],[17,1]],performAction:(0,i.K2)((function t(e,n,i,r,s,o,a){var l=o.length-1;switch(s){case 7:const t=r.findOrCreateNode(o[l-4].trim().replaceAll('""','"'));const e=r.findOrCreateNode(o[l-2].trim().replaceAll('""','"'));const n=parseFloat(o[l].trim());r.addLink(t,e,n);break;case 8:case 9:case 11:this.$=o[l];break;case 10:this.$=o[l-1];break}}),"anonymous"),table:[{3:1,4:[1,2]},{1:[3]},{5:[1,3]},{6:4,8:5,15:6,16:7,17:8,18:e,20:n},{1:[2,6],7:11,10:[1,12]},t(n,[2,4],{9:13,5:[1,14]}),{12:[1,15]},t(r,[2,8]),t(r,[2,9]),{19:[1,16]},t(r,[2,11]),{1:[2,1]},{1:[2,5]},t(n,[2,2]),{6:17,8:5,15:6,16:7,17:8,18:e,20:n},{15:18,16:7,17:8,18:e,20:n},{18:[1,19]},t(n,[2,3]),{12:[1,20]},t(r,[2,10]),{15:21,16:7,17:8,18:e,20:n},t([1,5,10],[2,7])],defaultActions:{11:[2,1],12:[2,5]},parseError:(0,i.K2)((function t(e,n){if(n.recoverable){this.trace(e)}else{var i=new Error(e);i.hash=n;throw i}}),"parseError"),parse:(0,i.K2)((function t(e){var n=this,r=[0],s=[],o=[null],a=[],l=this.table,c="",h=0,u=0,f=0,y=2,d=1;var p=a.slice.call(arguments,1);var g=Object.create(this.lexer);var _={yy:{}};for(var k in this.yy){if(Object.prototype.hasOwnProperty.call(this.yy,k)){_.yy[k]=this.yy[k]}}g.setInput(e,_.yy);_.yy.lexer=g;_.yy.parser=this;if(typeof g.yylloc=="undefined"){g.yylloc={}}var x=g.yylloc;a.push(x);var m=g.options&&g.options.ranges;if(typeof _.yy.parseError==="function"){this.parseError=_.yy.parseError}else{this.parseError=Object.getPrototypeOf(this).parseError}function v(t){r.length=r.length-2*t;o.length=o.length-t;a.length=a.length-t}(0,i.K2)(v,"popStack");function b(){var t;t=s.pop()||g.lex()||d;if(typeof t!=="number"){if(t instanceof Array){s=t;t=s.pop()}t=n.symbols_[t]||t}return t}(0,i.K2)(b,"lex");var w,L,S,E,K,A,M={},I,T,C,D;while(true){S=r[r.length-1];if(this.defaultActions[S]){E=this.defaultActions[S]}else{if(w===null||typeof w=="undefined"){w=b()}E=l[S]&&l[S][w]}if(typeof E==="undefined"||!E.length||!E[0]){var N="";D=[];for(I in l[S]){if(this.terminals_[I]&&I>y){D.push("'"+this.terminals_[I]+"'")}}if(g.showPosition){N="Parse error on line "+(h+1)+":\n"+g.showPosition()+"\nExpecting "+D.join(", ")+", got '"+(this.terminals_[w]||w)+"'"}else{N="Parse error on line "+(h+1)+": Unexpected "+(w==d?"end of input":"'"+(this.terminals_[w]||w)+"'")}this.parseError(N,{text:g.match,token:this.terminals_[w]||w,line:g.yylineno,loc:x,expected:D})}if(E[0]instanceof Array&&E.length>1){throw new Error("Parse Error: multiple actions possible at state: "+S+", token: "+w)}switch(E[0]){case 1:r.push(w);o.push(g.yytext);a.push(g.yylloc);r.push(E[1]);w=null;if(!L){u=g.yyleng;c=g.yytext;h=g.yylineno;x=g.yylloc;if(f>0){f--}}else{w=L;L=null}break;case 2:T=this.productions_[E[1]][1];M.$=o[o.length-T];M._$={first_line:a[a.length-(T||1)].first_line,last_line:a[a.length-1].last_line,first_column:a[a.length-(T||1)].first_column,last_column:a[a.length-1].last_column};if(m){M._$.range=[a[a.length-(T||1)].range[0],a[a.length-1].range[1]]}A=this.performAction.apply(M,[c,u,h,_.yy,E[1],o,a].concat(p));if(typeof A!=="undefined"){return A}if(T){r=r.slice(0,-1*T*2);o=o.slice(0,-1*T);a=a.slice(0,-1*T)}r.push(this.productions_[E[1]][0]);o.push(M.$);a.push(M._$);C=l[r[r.length-2]][r[r.length-1]];r.push(C);break;case 3:return true}}return true}),"parse")};var o=function(){var t={EOF:1,parseError:(0,i.K2)((function t(e,n){if(this.yy.parser){this.yy.parser.parseError(e,n)}else{throw new Error(e)}}),"parseError"),setInput:(0,i.K2)((function(t,e){this.yy=e||this.yy||{};this._input=t;this._more=this._backtrack=this.done=false;this.yylineno=this.yyleng=0;this.yytext=this.matched=this.match="";this.conditionStack=["INITIAL"];this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0};if(this.options.ranges){this.yylloc.range=[0,0]}this.offset=0;return this}),"setInput"),input:(0,i.K2)((function(){var t=this._input[0];this.yytext+=t;this.yyleng++;this.offset++;this.match+=t;this.matched+=t;var e=t.match(/(?:\r\n?|\n).*/g);if(e){this.yylineno++;this.yylloc.last_line++}else{this.yylloc.last_column++}if(this.options.ranges){this.yylloc.range[1]++}this._input=this._input.slice(1);return t}),"input"),unput:(0,i.K2)((function(t){var e=t.length;var n=t.split(/(?:\r\n?|\n)/g);this._input=t+this._input;this.yytext=this.yytext.substr(0,this.yytext.length-e);this.offset-=e;var i=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1);this.matched=this.matched.substr(0,this.matched.length-1);if(n.length-1){this.yylineno-=n.length-1}var r=this.yylloc.range;this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:n?(n.length===i.length?this.yylloc.first_column:0)+i[i.length-n.length].length-n[0].length:this.yylloc.first_column-e};if(this.options.ranges){this.yylloc.range=[r[0],r[0]+this.yyleng-e]}this.yyleng=this.yytext.length;return this}),"unput"),more:(0,i.K2)((function(){this._more=true;return this}),"more"),reject:(0,i.K2)((function(){if(this.options.backtrack_lexer){this._backtrack=true}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}return this}),"reject"),less:(0,i.K2)((function(t){this.unput(this.match.slice(t))}),"less"),pastInput:(0,i.K2)((function(){var t=this.matched.substr(0,this.matched.length-this.match.length);return(t.length>20?"...":"")+t.substr(-20).replace(/\n/g,"")}),"pastInput"),upcomingInput:(0,i.K2)((function(){var t=this.match;if(t.length<20){t+=this._input.substr(0,20-t.length)}return(t.substr(0,20)+(t.length>20?"...":"")).replace(/\n/g,"")}),"upcomingInput"),showPosition:(0,i.K2)((function(){var t=this.pastInput();var e=new Array(t.length+1).join("-");return t+this.upcomingInput()+"\n"+e+"^"}),"showPosition"),test_match:(0,i.K2)((function(t,e){var n,i,r;if(this.options.backtrack_lexer){r={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done};if(this.options.ranges){r.yylloc.range=this.yylloc.range.slice(0)}}i=t[0].match(/(?:\r\n?|\n).*/g);if(i){this.yylineno+=i.length}this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:i?i[i.length-1].length-i[i.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+t[0].length};this.yytext+=t[0];this.match+=t[0];this.matches=t;this.yyleng=this.yytext.length;if(this.options.ranges){this.yylloc.range=[this.offset,this.offset+=this.yyleng]}this._more=false;this._backtrack=false;this._input=this._input.slice(t[0].length);this.matched+=t[0];n=this.performAction.call(this,this.yy,this,e,this.conditionStack[this.conditionStack.length-1]);if(this.done&&this._input){this.done=false}if(n){return n}else if(this._backtrack){for(var s in r){this[s]=r[s]}return false}return false}),"test_match"),next:(0,i.K2)((function(){if(this.done){return this.EOF}if(!this._input){this.done=true}var t,e,n,i;if(!this._more){this.yytext="";this.match=""}var r=this._currentRules();for(var s=0;s<r.length;s++){n=this._input.match(this.rules[r[s]]);if(n&&(!e||n[0].length>e[0].length)){e=n;i=s;if(this.options.backtrack_lexer){t=this.test_match(n,r[s]);if(t!==false){return t}else if(this._backtrack){e=false;continue}else{return false}}else if(!this.options.flex){break}}}if(e){t=this.test_match(e,r[i]);if(t!==false){return t}return false}if(this._input===""){return this.EOF}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}}),"next"),lex:(0,i.K2)((function t(){var e=this.next();if(e){return e}else{return this.lex()}}),"lex"),begin:(0,i.K2)((function t(e){this.conditionStack.push(e)}),"begin"),popState:(0,i.K2)((function t(){var e=this.conditionStack.length-1;if(e>0){return this.conditionStack.pop()}else{return this.conditionStack[0]}}),"popState"),_currentRules:(0,i.K2)((function t(){if(this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules}else{return this.conditions["INITIAL"].rules}}),"_currentRules"),topState:(0,i.K2)((function t(e){e=this.conditionStack.length-1-Math.abs(e||0);if(e>=0){return this.conditionStack[e]}else{return"INITIAL"}}),"topState"),pushState:(0,i.K2)((function t(e){this.begin(e)}),"pushState"),stateStackSize:(0,i.K2)((function t(){return this.conditionStack.length}),"stateStackSize"),options:{"case-insensitive":true},performAction:(0,i.K2)((function t(e,n,i,r){var s=r;switch(i){case 0:this.pushState("csv");return 4;break;case 1:return 10;break;case 2:return 5;break;case 3:return 12;break;case 4:this.pushState("escaped_text");return 18;break;case 5:return 20;break;case 6:this.popState("escaped_text");return 18;break;case 7:return 19;break}}),"anonymous"),rules:[/^(?:sankey-beta\b)/i,/^(?:$)/i,/^(?:((\u000D\u000A)|(\u000A)))/i,/^(?:(\u002C))/i,/^(?:(\u0022))/i,/^(?:([\u0020-\u0021\u0023-\u002B\u002D-\u007E])*)/i,/^(?:(\u0022)(?!(\u0022)))/i,/^(?:(([\u0020-\u0021\u0023-\u002B\u002D-\u007E])|(\u002C)|(\u000D)|(\u000A)|(\u0022)(\u0022))*)/i],conditions:{csv:{rules:[1,2,3,4,5,6,7],inclusive:false},escaped_text:{rules:[6,7],inclusive:false},INITIAL:{rules:[0,1,2,3,4,5,6,7],inclusive:true}}};return t}();s.lexer=o;function a(){this.yy={}}(0,i.K2)(a,"Parser");a.prototype=s;s.Parser=a;return new a}();Y.parser=Y;var q=Y;var Q=[];var B=[];var Z=new Map;var H=(0,i.K2)((()=>{Q=[];B=[];Z=new Map;(0,i.IU)()}),"clear");var J=class{constructor(t,e,n=0){this.source=t;this.target=e;this.value=n}static{(0,i.K2)(this,"SankeyLink")}};var tt=(0,i.K2)(((t,e,n)=>{Q.push(new J(t,e,n))}),"addLink");var et=class{constructor(t){this.ID=t}static{(0,i.K2)(this,"SankeyNode")}};var nt=(0,i.K2)((t=>{t=i.Y2.sanitizeText(t,(0,i.D7)());let e=Z.get(t);if(e===void 0){e=new et(t);Z.set(t,e);B.push(e)}return e}),"findOrCreateNode");var it=(0,i.K2)((()=>B),"getNodes");var rt=(0,i.K2)((()=>Q),"getLinks");var st=(0,i.K2)((()=>({nodes:B.map((t=>({id:t.ID}))),links:Q.map((t=>({source:t.source.ID,target:t.target.ID,value:t.value})))})),"getGraph");var ot={nodesMap:Z,getConfig:(0,i.K2)((()=>(0,i.D7)().sankey),"getConfig"),getNodes:it,getLinks:rt,getGraph:st,addLink:tt,findOrCreateNode:nt,getAccTitle:i.iN,setAccTitle:i.SV,getAccDescription:i.m7,setAccDescription:i.EI,getDiagramTitle:i.ab,setDiagramTitle:i.ke,clear:H};var at=class t{static{(0,i.K2)(this,"Uid")}static{this.count=0}static next(e){return new t(e+ ++t.count)}constructor(t){this.id=t;this.href=`#${t}`}toString(){return"url("+this.href+")"}};var lt={left:a,right:l,center:h,justify:c};var ct=(0,i.K2)((function(t,e,n,s){const{securityLevel:o,sankey:a}=(0,i.D7)();const l=i.ME.sankey;let c;if(o==="sandbox"){c=(0,r.Ltv)("#i"+e)}const h=o==="sandbox"?(0,r.Ltv)(c.nodes()[0].contentDocument.body):(0,r.Ltv)("body");const u=o==="sandbox"?h.select(`[id="${e}"]`):(0,r.Ltv)(`[id="${e}"]`);const f=a?.width??l.width;const y=a?.height??l.width;const d=a?.useMaxWidth??l.useMaxWidth;const p=a?.nodeAlignment??l.nodeAlignment;const g=a?.prefix??l.prefix;const _=a?.suffix??l.suffix;const k=a?.showValues??l.showValues;const x=s.db.getGraph();const m=lt[p];const v=10;const b=w().nodeId((t=>t.id)).nodeWidth(v).nodePadding(10+(k?15:0)).nodeAlign(m).extent([[0,0],[f,y]]);b(x);const L=(0,r.UMr)(r.zt);u.append("g").attr("class","nodes").selectAll(".node").data(x.nodes).join("g").attr("class","node").attr("id",(t=>(t.uid=at.next("node-")).id)).attr("transform",(function(t){return"translate("+t.x0+","+t.y0+")"})).attr("x",(t=>t.x0)).attr("y",(t=>t.y0)).append("rect").attr("height",(t=>t.y1-t.y0)).attr("width",(t=>t.x1-t.x0)).attr("fill",(t=>L(t.id)));const S=(0,i.K2)((({id:t,value:e})=>{if(!k){return t}return`${t}\n${g}${Math.round(e*100)/100}${_}`}),"getText");u.append("g").attr("class","node-labels").attr("font-size",14).selectAll("text").data(x.nodes).join("text").attr("x",(t=>t.x0<f/2?t.x1+6:t.x0-6)).attr("y",(t=>(t.y1+t.y0)/2)).attr("dy",`${k?"0":"0.35"}em`).attr("text-anchor",(t=>t.x0<f/2?"start":"end")).text(S);const E=u.append("g").attr("class","links").attr("fill","none").attr("stroke-opacity",.5).selectAll(".link").data(x.links).join("g").attr("class","link").style("mix-blend-mode","multiply");const K=a?.linkColor??"gradient";if(K==="gradient"){const t=E.append("linearGradient").attr("id",(t=>(t.uid=at.next("linearGradient-")).id)).attr("gradientUnits","userSpaceOnUse").attr("x1",(t=>t.source.x1)).attr("x2",(t=>t.target.x0));t.append("stop").attr("offset","0%").attr("stop-color",(t=>L(t.source.id)));t.append("stop").attr("offset","100%").attr("stop-color",(t=>L(t.target.id)))}let A;switch(K){case"gradient":A=(0,i.K2)((t=>t.uid),"coloring");break;case"source":A=(0,i.K2)((t=>L(t.source.id)),"coloring");break;case"target":A=(0,i.K2)((t=>L(t.target.id)),"coloring");break;default:A=K}E.append("path").attr("d",X()).attr("stroke",A).attr("stroke-width",(t=>Math.max(1,t.width)));(0,i.ot)(void 0,u,0,d)}),"draw");var ht={draw:ct};var ut=(0,i.K2)((t=>{const e=t.replaceAll(/^[^\S\n\r]+|[^\S\n\r]+$/g,"").replaceAll(/([\n\r])+/g,"\n").trim();return e}),"prepareTextForParsing");var ft=(0,i.K2)((t=>`.label {\n      font-family: ${t.fontFamily};\n    }`),"getStyles");var yt=ft;var dt=q.parse.bind(q);q.parse=t=>dt(ut(t));var pt={styles:yt,parser:q,db:ot,renderer:ht}}}]);