"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[580],{15051:(e,t,s)=>{s.d(t,{A:()=>n,P:()=>a});var i=s(75905);var r=s(24982);var n=(0,i.K2)(((e,t)=>{let s;if(t==="sandbox"){s=(0,r.Ltv)("#i"+e)}const i=t==="sandbox"?(0,r.Ltv)(s.nodes()[0].contentDocument.body):(0,r.Ltv)("body");const n=i.select(`[id="${e}"]`);return n}),"getDiagramElement");var a=(0,i.K2)(((e,t,s,r)=>{e.attr("class",s);const{width:n,height:a,x:o,y:h}=l(e,t);(0,i.a$)(e,a,n,r);const u=c(o,h,n,a,t);e.attr("viewBox",u);i.Rm.debug(`viewBox configured: ${u} with padding: ${t}`)}),"setupViewPortForSVG");var l=(0,i.K2)(((e,t)=>{const s=e.node()?.getBBox()||{width:0,height:0,x:0,y:0};return{width:s.width+t*2,height:s.height+t*2,x:s.x,y:s.y}}),"calculateDimensionsWithPadding");var c=(0,i.K2)(((e,t,s,i,r)=>`${e-r} ${t-r} ${s} ${i}`),"createViewBox")},90580:(e,t,s)=>{s.d(t,{diagram:()=>k});var i=s(15051);var r=s(94065);var n=s(33416);var a=s(94746);var l=s(20778);var c=s(57590);var o=s(68232);var h=s(76261);var u=s(96049);var y=s(75905);var f=function(){var e=(0,y.K2)((function(e,t,s,i){for(s=s||{},i=e.length;i--;s[e[i]]=t);return s}),"o"),t=[1,3],s=[1,4],i=[1,5],r=[1,6],n=[5,6,8,9,11,13,21,22,23,24,41,42,43,44,45,46,54,72,74,77,89,90],a=[1,22],l=[2,7],c=[1,26],o=[1,27],h=[1,28],u=[1,29],f=[1,33],m=[1,34],p=[1,35],d=[1,36],E=[1,37],b=[1,38],R=[1,24],k=[1,31],_=[1,32],g=[1,30],S=[1,39],I=[1,40],T=[5,8,9,11,13,21,22,23,24,41,42,43,44,45,46,54,72,74,77,89,90],N=[1,61],v=[89,90],q=[5,8,9,11,13,21,22,23,24,27,29,41,42,43,44,45,46,54,61,63,72,74,75,76,77,80,81,82,83,84,85,86,87,88,89,90],A=[27,29],C=[1,70],w=[1,71],x=[1,72],L=[1,73],D=[1,74],O=[1,75],$=[1,76],M=[1,83],F=[1,80],K=[1,84],P=[1,85],V=[1,86],U=[1,87],Y=[1,88],B=[1,89],Q=[1,90],H=[1,91],j=[1,92],W=[5,8,9,11,13,21,22,23,24,27,41,42,43,44,45,46,54,72,74,75,76,77,80,81,82,83,84,85,86,87,88,89,90],G=[63,64],z=[1,101],X=[5,8,9,11,13,21,22,23,24,41,42,43,44,45,46,54,72,74,76,77,89,90],J=[5,8,9,11,13,21,22,23,24,41,42,43,44,45,46,54,72,74,75,76,77,80,81,82,83,84,85,86,87,88,89,90],Z=[1,110],ee=[1,106],te=[1,107],se=[1,108],ie=[1,109],re=[1,111],ne=[1,116],ae=[1,117],le=[1,114],ce=[1,115];var oe={trace:(0,y.K2)((function e(){}),"trace"),yy:{},symbols_:{error:2,start:3,directive:4,NEWLINE:5,RD:6,diagram:7,EOF:8,acc_title:9,acc_title_value:10,acc_descr:11,acc_descr_value:12,acc_descr_multiline_value:13,requirementDef:14,elementDef:15,relationshipDef:16,direction:17,styleStatement:18,classDefStatement:19,classStatement:20,direction_tb:21,direction_bt:22,direction_rl:23,direction_lr:24,requirementType:25,requirementName:26,STRUCT_START:27,requirementBody:28,STYLE_SEPARATOR:29,idList:30,ID:31,COLONSEP:32,id:33,TEXT:34,text:35,RISK:36,riskLevel:37,VERIFYMTHD:38,verifyType:39,STRUCT_STOP:40,REQUIREMENT:41,FUNCTIONAL_REQUIREMENT:42,INTERFACE_REQUIREMENT:43,PERFORMANCE_REQUIREMENT:44,PHYSICAL_REQUIREMENT:45,DESIGN_CONSTRAINT:46,LOW_RISK:47,MED_RISK:48,HIGH_RISK:49,VERIFY_ANALYSIS:50,VERIFY_DEMONSTRATION:51,VERIFY_INSPECTION:52,VERIFY_TEST:53,ELEMENT:54,elementName:55,elementBody:56,TYPE:57,type:58,DOCREF:59,ref:60,END_ARROW_L:61,relationship:62,LINE:63,END_ARROW_R:64,CONTAINS:65,COPIES:66,DERIVES:67,SATISFIES:68,VERIFIES:69,REFINES:70,TRACES:71,CLASSDEF:72,stylesOpt:73,CLASS:74,ALPHA:75,COMMA:76,STYLE:77,style:78,styleComponent:79,NUM:80,COLON:81,UNIT:82,SPACE:83,BRKT:84,PCT:85,MINUS:86,LABEL:87,SEMICOLON:88,unqString:89,qString:90,$accept:0,$end:1},terminals_:{2:"error",5:"NEWLINE",6:"RD",8:"EOF",9:"acc_title",10:"acc_title_value",11:"acc_descr",12:"acc_descr_value",13:"acc_descr_multiline_value",21:"direction_tb",22:"direction_bt",23:"direction_rl",24:"direction_lr",27:"STRUCT_START",29:"STYLE_SEPARATOR",31:"ID",32:"COLONSEP",34:"TEXT",36:"RISK",38:"VERIFYMTHD",40:"STRUCT_STOP",41:"REQUIREMENT",42:"FUNCTIONAL_REQUIREMENT",43:"INTERFACE_REQUIREMENT",44:"PERFORMANCE_REQUIREMENT",45:"PHYSICAL_REQUIREMENT",46:"DESIGN_CONSTRAINT",47:"LOW_RISK",48:"MED_RISK",49:"HIGH_RISK",50:"VERIFY_ANALYSIS",51:"VERIFY_DEMONSTRATION",52:"VERIFY_INSPECTION",53:"VERIFY_TEST",54:"ELEMENT",57:"TYPE",59:"DOCREF",61:"END_ARROW_L",63:"LINE",64:"END_ARROW_R",65:"CONTAINS",66:"COPIES",67:"DERIVES",68:"SATISFIES",69:"VERIFIES",70:"REFINES",71:"TRACES",72:"CLASSDEF",74:"CLASS",75:"ALPHA",76:"COMMA",77:"STYLE",80:"NUM",81:"COLON",82:"UNIT",83:"SPACE",84:"BRKT",85:"PCT",86:"MINUS",87:"LABEL",88:"SEMICOLON",89:"unqString",90:"qString"},productions_:[0,[3,3],[3,2],[3,4],[4,2],[4,2],[4,1],[7,0],[7,2],[7,2],[7,2],[7,2],[7,2],[7,2],[7,2],[7,2],[7,2],[17,1],[17,1],[17,1],[17,1],[14,5],[14,7],[28,5],[28,5],[28,5],[28,5],[28,2],[28,1],[25,1],[25,1],[25,1],[25,1],[25,1],[25,1],[37,1],[37,1],[37,1],[39,1],[39,1],[39,1],[39,1],[15,5],[15,7],[56,5],[56,5],[56,2],[56,1],[16,5],[16,5],[62,1],[62,1],[62,1],[62,1],[62,1],[62,1],[62,1],[19,3],[20,3],[20,3],[30,1],[30,3],[30,1],[30,3],[18,3],[73,1],[73,3],[78,1],[78,2],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[26,1],[26,1],[33,1],[33,1],[35,1],[35,1],[55,1],[55,1],[58,1],[58,1],[60,1],[60,1]],performAction:(0,y.K2)((function e(t,s,i,r,n,a,l){var c=a.length-1;switch(n){case 4:this.$=a[c].trim();r.setAccTitle(this.$);break;case 5:case 6:this.$=a[c].trim();r.setAccDescription(this.$);break;case 7:this.$=[];break;case 17:r.setDirection("TB");break;case 18:r.setDirection("BT");break;case 19:r.setDirection("RL");break;case 20:r.setDirection("LR");break;case 21:r.addRequirement(a[c-3],a[c-4]);break;case 22:r.addRequirement(a[c-5],a[c-6]);r.setClass([a[c-5]],a[c-3]);break;case 23:r.setNewReqId(a[c-2]);break;case 24:r.setNewReqText(a[c-2]);break;case 25:r.setNewReqRisk(a[c-2]);break;case 26:r.setNewReqVerifyMethod(a[c-2]);break;case 29:this.$=r.RequirementType.REQUIREMENT;break;case 30:this.$=r.RequirementType.FUNCTIONAL_REQUIREMENT;break;case 31:this.$=r.RequirementType.INTERFACE_REQUIREMENT;break;case 32:this.$=r.RequirementType.PERFORMANCE_REQUIREMENT;break;case 33:this.$=r.RequirementType.PHYSICAL_REQUIREMENT;break;case 34:this.$=r.RequirementType.DESIGN_CONSTRAINT;break;case 35:this.$=r.RiskLevel.LOW_RISK;break;case 36:this.$=r.RiskLevel.MED_RISK;break;case 37:this.$=r.RiskLevel.HIGH_RISK;break;case 38:this.$=r.VerifyType.VERIFY_ANALYSIS;break;case 39:this.$=r.VerifyType.VERIFY_DEMONSTRATION;break;case 40:this.$=r.VerifyType.VERIFY_INSPECTION;break;case 41:this.$=r.VerifyType.VERIFY_TEST;break;case 42:r.addElement(a[c-3]);break;case 43:r.addElement(a[c-5]);r.setClass([a[c-5]],a[c-3]);break;case 44:r.setNewElementType(a[c-2]);break;case 45:r.setNewElementDocRef(a[c-2]);break;case 48:r.addRelationship(a[c-2],a[c],a[c-4]);break;case 49:r.addRelationship(a[c-2],a[c-4],a[c]);break;case 50:this.$=r.Relationships.CONTAINS;break;case 51:this.$=r.Relationships.COPIES;break;case 52:this.$=r.Relationships.DERIVES;break;case 53:this.$=r.Relationships.SATISFIES;break;case 54:this.$=r.Relationships.VERIFIES;break;case 55:this.$=r.Relationships.REFINES;break;case 56:this.$=r.Relationships.TRACES;break;case 57:this.$=a[c-2];r.defineClass(a[c-1],a[c]);break;case 58:r.setClass(a[c-1],a[c]);break;case 59:r.setClass([a[c-2]],a[c]);break;case 60:case 62:this.$=[a[c]];break;case 61:case 63:this.$=a[c-2].concat([a[c]]);break;case 64:this.$=a[c-2];r.setCssStyle(a[c-1],a[c]);break;case 65:this.$=[a[c]];break;case 66:a[c-2].push(a[c]);this.$=a[c-2];break;case 68:this.$=a[c-1]+a[c];break}}),"anonymous"),table:[{3:1,4:2,6:t,9:s,11:i,13:r},{1:[3]},{3:8,4:2,5:[1,7],6:t,9:s,11:i,13:r},{5:[1,9]},{10:[1,10]},{12:[1,11]},e(n,[2,6]),{3:12,4:2,6:t,9:s,11:i,13:r},{1:[2,2]},{4:17,5:a,7:13,8:l,9:s,11:i,13:r,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:c,22:o,23:h,24:u,25:23,33:25,41:f,42:m,43:p,44:d,45:E,46:b,54:R,72:k,74:_,77:g,89:S,90:I},e(n,[2,4]),e(n,[2,5]),{1:[2,1]},{8:[1,41]},{4:17,5:a,7:42,8:l,9:s,11:i,13:r,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:c,22:o,23:h,24:u,25:23,33:25,41:f,42:m,43:p,44:d,45:E,46:b,54:R,72:k,74:_,77:g,89:S,90:I},{4:17,5:a,7:43,8:l,9:s,11:i,13:r,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:c,22:o,23:h,24:u,25:23,33:25,41:f,42:m,43:p,44:d,45:E,46:b,54:R,72:k,74:_,77:g,89:S,90:I},{4:17,5:a,7:44,8:l,9:s,11:i,13:r,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:c,22:o,23:h,24:u,25:23,33:25,41:f,42:m,43:p,44:d,45:E,46:b,54:R,72:k,74:_,77:g,89:S,90:I},{4:17,5:a,7:45,8:l,9:s,11:i,13:r,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:c,22:o,23:h,24:u,25:23,33:25,41:f,42:m,43:p,44:d,45:E,46:b,54:R,72:k,74:_,77:g,89:S,90:I},{4:17,5:a,7:46,8:l,9:s,11:i,13:r,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:c,22:o,23:h,24:u,25:23,33:25,41:f,42:m,43:p,44:d,45:E,46:b,54:R,72:k,74:_,77:g,89:S,90:I},{4:17,5:a,7:47,8:l,9:s,11:i,13:r,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:c,22:o,23:h,24:u,25:23,33:25,41:f,42:m,43:p,44:d,45:E,46:b,54:R,72:k,74:_,77:g,89:S,90:I},{4:17,5:a,7:48,8:l,9:s,11:i,13:r,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:c,22:o,23:h,24:u,25:23,33:25,41:f,42:m,43:p,44:d,45:E,46:b,54:R,72:k,74:_,77:g,89:S,90:I},{4:17,5:a,7:49,8:l,9:s,11:i,13:r,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:c,22:o,23:h,24:u,25:23,33:25,41:f,42:m,43:p,44:d,45:E,46:b,54:R,72:k,74:_,77:g,89:S,90:I},{4:17,5:a,7:50,8:l,9:s,11:i,13:r,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:c,22:o,23:h,24:u,25:23,33:25,41:f,42:m,43:p,44:d,45:E,46:b,54:R,72:k,74:_,77:g,89:S,90:I},{26:51,89:[1,52],90:[1,53]},{55:54,89:[1,55],90:[1,56]},{29:[1,59],61:[1,57],63:[1,58]},e(T,[2,17]),e(T,[2,18]),e(T,[2,19]),e(T,[2,20]),{30:60,33:62,75:N,89:S,90:I},{30:63,33:62,75:N,89:S,90:I},{30:64,33:62,75:N,89:S,90:I},e(v,[2,29]),e(v,[2,30]),e(v,[2,31]),e(v,[2,32]),e(v,[2,33]),e(v,[2,34]),e(q,[2,81]),e(q,[2,82]),{1:[2,3]},{8:[2,8]},{8:[2,9]},{8:[2,10]},{8:[2,11]},{8:[2,12]},{8:[2,13]},{8:[2,14]},{8:[2,15]},{8:[2,16]},{27:[1,65],29:[1,66]},e(A,[2,79]),e(A,[2,80]),{27:[1,67],29:[1,68]},e(A,[2,85]),e(A,[2,86]),{62:69,65:C,66:w,67:x,68:L,69:D,70:O,71:$},{62:77,65:C,66:w,67:x,68:L,69:D,70:O,71:$},{30:78,33:62,75:N,89:S,90:I},{73:79,75:M,76:F,78:81,79:82,80:K,81:P,82:V,83:U,84:Y,85:B,86:Q,87:H,88:j},e(W,[2,60]),e(W,[2,62]),{73:93,75:M,76:F,78:81,79:82,80:K,81:P,82:V,83:U,84:Y,85:B,86:Q,87:H,88:j},{30:94,33:62,75:N,76:F,89:S,90:I},{5:[1,95]},{30:96,33:62,75:N,89:S,90:I},{5:[1,97]},{30:98,33:62,75:N,89:S,90:I},{63:[1,99]},e(G,[2,50]),e(G,[2,51]),e(G,[2,52]),e(G,[2,53]),e(G,[2,54]),e(G,[2,55]),e(G,[2,56]),{64:[1,100]},e(T,[2,59],{76:F}),e(T,[2,64],{76:z}),{33:103,75:[1,102],89:S,90:I},e(X,[2,65],{79:104,75:M,80:K,81:P,82:V,83:U,84:Y,85:B,86:Q,87:H,88:j}),e(J,[2,67]),e(J,[2,69]),e(J,[2,70]),e(J,[2,71]),e(J,[2,72]),e(J,[2,73]),e(J,[2,74]),e(J,[2,75]),e(J,[2,76]),e(J,[2,77]),e(J,[2,78]),e(T,[2,57],{76:z}),e(T,[2,58],{76:F}),{5:Z,28:105,31:ee,34:te,36:se,38:ie,40:re},{27:[1,112],76:F},{5:ne,40:ae,56:113,57:le,59:ce},{27:[1,118],76:F},{33:119,89:S,90:I},{33:120,89:S,90:I},{75:M,78:121,79:82,80:K,81:P,82:V,83:U,84:Y,85:B,86:Q,87:H,88:j},e(W,[2,61]),e(W,[2,63]),e(J,[2,68]),e(T,[2,21]),{32:[1,122]},{32:[1,123]},{32:[1,124]},{32:[1,125]},{5:Z,28:126,31:ee,34:te,36:se,38:ie,40:re},e(T,[2,28]),{5:[1,127]},e(T,[2,42]),{32:[1,128]},{32:[1,129]},{5:ne,40:ae,56:130,57:le,59:ce},e(T,[2,47]),{5:[1,131]},e(T,[2,48]),e(T,[2,49]),e(X,[2,66],{79:104,75:M,80:K,81:P,82:V,83:U,84:Y,85:B,86:Q,87:H,88:j}),{33:132,89:S,90:I},{35:133,89:[1,134],90:[1,135]},{37:136,47:[1,137],48:[1,138],49:[1,139]},{39:140,50:[1,141],51:[1,142],52:[1,143],53:[1,144]},e(T,[2,27]),{5:Z,28:145,31:ee,34:te,36:se,38:ie,40:re},{58:146,89:[1,147],90:[1,148]},{60:149,89:[1,150],90:[1,151]},e(T,[2,46]),{5:ne,40:ae,56:152,57:le,59:ce},{5:[1,153]},{5:[1,154]},{5:[2,83]},{5:[2,84]},{5:[1,155]},{5:[2,35]},{5:[2,36]},{5:[2,37]},{5:[1,156]},{5:[2,38]},{5:[2,39]},{5:[2,40]},{5:[2,41]},e(T,[2,22]),{5:[1,157]},{5:[2,87]},{5:[2,88]},{5:[1,158]},{5:[2,89]},{5:[2,90]},e(T,[2,43]),{5:Z,28:159,31:ee,34:te,36:se,38:ie,40:re},{5:Z,28:160,31:ee,34:te,36:se,38:ie,40:re},{5:Z,28:161,31:ee,34:te,36:se,38:ie,40:re},{5:Z,28:162,31:ee,34:te,36:se,38:ie,40:re},{5:ne,40:ae,56:163,57:le,59:ce},{5:ne,40:ae,56:164,57:le,59:ce},e(T,[2,23]),e(T,[2,24]),e(T,[2,25]),e(T,[2,26]),e(T,[2,44]),e(T,[2,45])],defaultActions:{8:[2,2],12:[2,1],41:[2,3],42:[2,8],43:[2,9],44:[2,10],45:[2,11],46:[2,12],47:[2,13],48:[2,14],49:[2,15],50:[2,16],134:[2,83],135:[2,84],137:[2,35],138:[2,36],139:[2,37],141:[2,38],142:[2,39],143:[2,40],144:[2,41],147:[2,87],148:[2,88],150:[2,89],151:[2,90]},parseError:(0,y.K2)((function e(t,s){if(s.recoverable){this.trace(t)}else{var i=new Error(t);i.hash=s;throw i}}),"parseError"),parse:(0,y.K2)((function e(t){var s=this,i=[0],r=[],n=[null],a=[],l=this.table,c="",o=0,h=0,u=0,f=2,m=1;var p=a.slice.call(arguments,1);var d=Object.create(this.lexer);var E={yy:{}};for(var b in this.yy){if(Object.prototype.hasOwnProperty.call(this.yy,b)){E.yy[b]=this.yy[b]}}d.setInput(t,E.yy);E.yy.lexer=d;E.yy.parser=this;if(typeof d.yylloc=="undefined"){d.yylloc={}}var R=d.yylloc;a.push(R);var k=d.options&&d.options.ranges;if(typeof E.yy.parseError==="function"){this.parseError=E.yy.parseError}else{this.parseError=Object.getPrototypeOf(this).parseError}function _(e){i.length=i.length-2*e;n.length=n.length-e;a.length=a.length-e}(0,y.K2)(_,"popStack");function g(){var e;e=r.pop()||d.lex()||m;if(typeof e!=="number"){if(e instanceof Array){r=e;e=r.pop()}e=s.symbols_[e]||e}return e}(0,y.K2)(g,"lex");var S,I,T,N,v,q,A={},C,w,x,L;while(true){T=i[i.length-1];if(this.defaultActions[T]){N=this.defaultActions[T]}else{if(S===null||typeof S=="undefined"){S=g()}N=l[T]&&l[T][S]}if(typeof N==="undefined"||!N.length||!N[0]){var D="";L=[];for(C in l[T]){if(this.terminals_[C]&&C>f){L.push("'"+this.terminals_[C]+"'")}}if(d.showPosition){D="Parse error on line "+(o+1)+":\n"+d.showPosition()+"\nExpecting "+L.join(", ")+", got '"+(this.terminals_[S]||S)+"'"}else{D="Parse error on line "+(o+1)+": Unexpected "+(S==m?"end of input":"'"+(this.terminals_[S]||S)+"'")}this.parseError(D,{text:d.match,token:this.terminals_[S]||S,line:d.yylineno,loc:R,expected:L})}if(N[0]instanceof Array&&N.length>1){throw new Error("Parse Error: multiple actions possible at state: "+T+", token: "+S)}switch(N[0]){case 1:i.push(S);n.push(d.yytext);a.push(d.yylloc);i.push(N[1]);S=null;if(!I){h=d.yyleng;c=d.yytext;o=d.yylineno;R=d.yylloc;if(u>0){u--}}else{S=I;I=null}break;case 2:w=this.productions_[N[1]][1];A.$=n[n.length-w];A._$={first_line:a[a.length-(w||1)].first_line,last_line:a[a.length-1].last_line,first_column:a[a.length-(w||1)].first_column,last_column:a[a.length-1].last_column};if(k){A._$.range=[a[a.length-(w||1)].range[0],a[a.length-1].range[1]]}q=this.performAction.apply(A,[c,h,o,E.yy,N[1],n,a].concat(p));if(typeof q!=="undefined"){return q}if(w){i=i.slice(0,-1*w*2);n=n.slice(0,-1*w);a=a.slice(0,-1*w)}i.push(this.productions_[N[1]][0]);n.push(A.$);a.push(A._$);x=l[i[i.length-2]][i[i.length-1]];i.push(x);break;case 3:return true}}return true}),"parse")};var he=function(){var e={EOF:1,parseError:(0,y.K2)((function e(t,s){if(this.yy.parser){this.yy.parser.parseError(t,s)}else{throw new Error(t)}}),"parseError"),setInput:(0,y.K2)((function(e,t){this.yy=t||this.yy||{};this._input=e;this._more=this._backtrack=this.done=false;this.yylineno=this.yyleng=0;this.yytext=this.matched=this.match="";this.conditionStack=["INITIAL"];this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0};if(this.options.ranges){this.yylloc.range=[0,0]}this.offset=0;return this}),"setInput"),input:(0,y.K2)((function(){var e=this._input[0];this.yytext+=e;this.yyleng++;this.offset++;this.match+=e;this.matched+=e;var t=e.match(/(?:\r\n?|\n).*/g);if(t){this.yylineno++;this.yylloc.last_line++}else{this.yylloc.last_column++}if(this.options.ranges){this.yylloc.range[1]++}this._input=this._input.slice(1);return e}),"input"),unput:(0,y.K2)((function(e){var t=e.length;var s=e.split(/(?:\r\n?|\n)/g);this._input=e+this._input;this.yytext=this.yytext.substr(0,this.yytext.length-t);this.offset-=t;var i=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1);this.matched=this.matched.substr(0,this.matched.length-1);if(s.length-1){this.yylineno-=s.length-1}var r=this.yylloc.range;this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:s?(s.length===i.length?this.yylloc.first_column:0)+i[i.length-s.length].length-s[0].length:this.yylloc.first_column-t};if(this.options.ranges){this.yylloc.range=[r[0],r[0]+this.yyleng-t]}this.yyleng=this.yytext.length;return this}),"unput"),more:(0,y.K2)((function(){this._more=true;return this}),"more"),reject:(0,y.K2)((function(){if(this.options.backtrack_lexer){this._backtrack=true}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}return this}),"reject"),less:(0,y.K2)((function(e){this.unput(this.match.slice(e))}),"less"),pastInput:(0,y.K2)((function(){var e=this.matched.substr(0,this.matched.length-this.match.length);return(e.length>20?"...":"")+e.substr(-20).replace(/\n/g,"")}),"pastInput"),upcomingInput:(0,y.K2)((function(){var e=this.match;if(e.length<20){e+=this._input.substr(0,20-e.length)}return(e.substr(0,20)+(e.length>20?"...":"")).replace(/\n/g,"")}),"upcomingInput"),showPosition:(0,y.K2)((function(){var e=this.pastInput();var t=new Array(e.length+1).join("-");return e+this.upcomingInput()+"\n"+t+"^"}),"showPosition"),test_match:(0,y.K2)((function(e,t){var s,i,r;if(this.options.backtrack_lexer){r={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done};if(this.options.ranges){r.yylloc.range=this.yylloc.range.slice(0)}}i=e[0].match(/(?:\r\n?|\n).*/g);if(i){this.yylineno+=i.length}this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:i?i[i.length-1].length-i[i.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+e[0].length};this.yytext+=e[0];this.match+=e[0];this.matches=e;this.yyleng=this.yytext.length;if(this.options.ranges){this.yylloc.range=[this.offset,this.offset+=this.yyleng]}this._more=false;this._backtrack=false;this._input=this._input.slice(e[0].length);this.matched+=e[0];s=this.performAction.call(this,this.yy,this,t,this.conditionStack[this.conditionStack.length-1]);if(this.done&&this._input){this.done=false}if(s){return s}else if(this._backtrack){for(var n in r){this[n]=r[n]}return false}return false}),"test_match"),next:(0,y.K2)((function(){if(this.done){return this.EOF}if(!this._input){this.done=true}var e,t,s,i;if(!this._more){this.yytext="";this.match=""}var r=this._currentRules();for(var n=0;n<r.length;n++){s=this._input.match(this.rules[r[n]]);if(s&&(!t||s[0].length>t[0].length)){t=s;i=n;if(this.options.backtrack_lexer){e=this.test_match(s,r[n]);if(e!==false){return e}else if(this._backtrack){t=false;continue}else{return false}}else if(!this.options.flex){break}}}if(t){e=this.test_match(t,r[i]);if(e!==false){return e}return false}if(this._input===""){return this.EOF}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}}),"next"),lex:(0,y.K2)((function e(){var t=this.next();if(t){return t}else{return this.lex()}}),"lex"),begin:(0,y.K2)((function e(t){this.conditionStack.push(t)}),"begin"),popState:(0,y.K2)((function e(){var t=this.conditionStack.length-1;if(t>0){return this.conditionStack.pop()}else{return this.conditionStack[0]}}),"popState"),_currentRules:(0,y.K2)((function e(){if(this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules}else{return this.conditions["INITIAL"].rules}}),"_currentRules"),topState:(0,y.K2)((function e(t){t=this.conditionStack.length-1-Math.abs(t||0);if(t>=0){return this.conditionStack[t]}else{return"INITIAL"}}),"topState"),pushState:(0,y.K2)((function e(t){this.begin(t)}),"pushState"),stateStackSize:(0,y.K2)((function e(){return this.conditionStack.length}),"stateStackSize"),options:{"case-insensitive":true},performAction:(0,y.K2)((function e(t,s,i,r){var n=r;switch(i){case 0:return"title";break;case 1:this.begin("acc_title");return 9;break;case 2:this.popState();return"acc_title_value";break;case 3:this.begin("acc_descr");return 11;break;case 4:this.popState();return"acc_descr_value";break;case 5:this.begin("acc_descr_multiline");break;case 6:this.popState();break;case 7:return"acc_descr_multiline_value";break;case 8:return 21;break;case 9:return 22;break;case 10:return 23;break;case 11:return 24;break;case 12:return 5;break;case 13:break;case 14:break;case 15:break;case 16:return 8;break;case 17:return 6;break;case 18:return 27;break;case 19:return 40;break;case 20:return 29;break;case 21:return 32;break;case 22:return 31;break;case 23:return 34;break;case 24:return 36;break;case 25:return 38;break;case 26:return 41;break;case 27:return 42;break;case 28:return 43;break;case 29:return 44;break;case 30:return 45;break;case 31:return 46;break;case 32:return 47;break;case 33:return 48;break;case 34:return 49;break;case 35:return 50;break;case 36:return 51;break;case 37:return 52;break;case 38:return 53;break;case 39:return 54;break;case 40:return 65;break;case 41:return 66;break;case 42:return 67;break;case 43:return 68;break;case 44:return 69;break;case 45:return 70;break;case 46:return 71;break;case 47:return 57;break;case 48:return 59;break;case 49:this.begin("style");return 77;break;case 50:return 75;break;case 51:return 81;break;case 52:return 88;break;case 53:return"PERCENT";break;case 54:return 86;break;case 55:return 84;break;case 56:break;case 57:this.begin("string");break;case 58:this.popState();break;case 59:this.begin("style");return 72;break;case 60:this.begin("style");return 74;break;case 61:return 61;break;case 62:return 64;break;case 63:return 63;break;case 64:this.begin("string");break;case 65:this.popState();break;case 66:return"qString";break;case 67:s.yytext=s.yytext.trim();return 89;break;case 68:return 75;break;case 69:return 80;break;case 70:return 76;break}}),"anonymous"),rules:[/^(?:title\s[^#\n;]+)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:.*direction\s+TB[^\n]*)/i,/^(?:.*direction\s+BT[^\n]*)/i,/^(?:.*direction\s+RL[^\n]*)/i,/^(?:.*direction\s+LR[^\n]*)/i,/^(?:(\r?\n)+)/i,/^(?:\s+)/i,/^(?:#[^\n]*)/i,/^(?:%[^\n]*)/i,/^(?:$)/i,/^(?:requirementDiagram\b)/i,/^(?:\{)/i,/^(?:\})/i,/^(?::{3})/i,/^(?::)/i,/^(?:id\b)/i,/^(?:text\b)/i,/^(?:risk\b)/i,/^(?:verifyMethod\b)/i,/^(?:requirement\b)/i,/^(?:functionalRequirement\b)/i,/^(?:interfaceRequirement\b)/i,/^(?:performanceRequirement\b)/i,/^(?:physicalRequirement\b)/i,/^(?:designConstraint\b)/i,/^(?:low\b)/i,/^(?:medium\b)/i,/^(?:high\b)/i,/^(?:analysis\b)/i,/^(?:demonstration\b)/i,/^(?:inspection\b)/i,/^(?:test\b)/i,/^(?:element\b)/i,/^(?:contains\b)/i,/^(?:copies\b)/i,/^(?:derives\b)/i,/^(?:satisfies\b)/i,/^(?:verifies\b)/i,/^(?:refines\b)/i,/^(?:traces\b)/i,/^(?:type\b)/i,/^(?:docref\b)/i,/^(?:style\b)/i,/^(?:\w+)/i,/^(?::)/i,/^(?:;)/i,/^(?:%)/i,/^(?:-)/i,/^(?:#)/i,/^(?: )/i,/^(?:["])/i,/^(?:\n)/i,/^(?:classDef\b)/i,/^(?:class\b)/i,/^(?:<-)/i,/^(?:->)/i,/^(?:-)/i,/^(?:["])/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?:[\w][^:,\r\n\{\<\>\-\=]*)/i,/^(?:\w+)/i,/^(?:[0-9]+)/i,/^(?:,)/i],conditions:{acc_descr_multiline:{rules:[6,7,68,69,70],inclusive:false},acc_descr:{rules:[4,68,69,70],inclusive:false},acc_title:{rules:[2,68,69,70],inclusive:false},style:{rules:[50,51,52,53,54,55,56,57,58,68,69,70],inclusive:false},unqString:{rules:[68,69,70],inclusive:false},token:{rules:[68,69,70],inclusive:false},string:{rules:[65,66,68,69,70],inclusive:false},INITIAL:{rules:[0,1,3,5,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,67,68,69,70],inclusive:true}}};return e}();oe.lexer=he;function ue(){this.yy={}}(0,y.K2)(ue,"Parser");ue.prototype=oe;oe.Parser=ue;return new ue}();f.parser=f;var m=f;var p=class{constructor(){this.relations=[];this.latestRequirement=this.getInitialRequirement();this.requirements=new Map;this.latestElement=this.getInitialElement();this.elements=new Map;this.classes=new Map;this.direction="TB";this.RequirementType={REQUIREMENT:"Requirement",FUNCTIONAL_REQUIREMENT:"Functional Requirement",INTERFACE_REQUIREMENT:"Interface Requirement",PERFORMANCE_REQUIREMENT:"Performance Requirement",PHYSICAL_REQUIREMENT:"Physical Requirement",DESIGN_CONSTRAINT:"Design Constraint"};this.RiskLevel={LOW_RISK:"Low",MED_RISK:"Medium",HIGH_RISK:"High"};this.VerifyType={VERIFY_ANALYSIS:"Analysis",VERIFY_DEMONSTRATION:"Demonstration",VERIFY_INSPECTION:"Inspection",VERIFY_TEST:"Test"};this.Relationships={CONTAINS:"contains",COPIES:"copies",DERIVES:"derives",SATISFIES:"satisfies",VERIFIES:"verifies",REFINES:"refines",TRACES:"traces"};this.setAccTitle=y.SV;this.getAccTitle=y.iN;this.setAccDescription=y.EI;this.getAccDescription=y.m7;this.setDiagramTitle=y.ke;this.getDiagramTitle=y.ab;this.getConfig=(0,y.K2)((()=>(0,y.D7)().requirement),"getConfig");this.clear();this.setDirection=this.setDirection.bind(this);this.addRequirement=this.addRequirement.bind(this);this.setNewReqId=this.setNewReqId.bind(this);this.setNewReqRisk=this.setNewReqRisk.bind(this);this.setNewReqText=this.setNewReqText.bind(this);this.setNewReqVerifyMethod=this.setNewReqVerifyMethod.bind(this);this.addElement=this.addElement.bind(this);this.setNewElementType=this.setNewElementType.bind(this);this.setNewElementDocRef=this.setNewElementDocRef.bind(this);this.addRelationship=this.addRelationship.bind(this);this.setCssStyle=this.setCssStyle.bind(this);this.setClass=this.setClass.bind(this);this.defineClass=this.defineClass.bind(this);this.setAccTitle=this.setAccTitle.bind(this);this.setAccDescription=this.setAccDescription.bind(this)}static{(0,y.K2)(this,"RequirementDB")}getDirection(){return this.direction}setDirection(e){this.direction=e}resetLatestRequirement(){this.latestRequirement=this.getInitialRequirement()}resetLatestElement(){this.latestElement=this.getInitialElement()}getInitialRequirement(){return{requirementId:"",text:"",risk:"",verifyMethod:"",name:"",type:"",cssStyles:[],classes:["default"]}}getInitialElement(){return{name:"",type:"",docRef:"",cssStyles:[],classes:["default"]}}addRequirement(e,t){if(!this.requirements.has(e)){this.requirements.set(e,{name:e,type:t,requirementId:this.latestRequirement.requirementId,text:this.latestRequirement.text,risk:this.latestRequirement.risk,verifyMethod:this.latestRequirement.verifyMethod,cssStyles:[],classes:["default"]})}this.resetLatestRequirement();return this.requirements.get(e)}getRequirements(){return this.requirements}setNewReqId(e){if(this.latestRequirement!==void 0){this.latestRequirement.requirementId=e}}setNewReqText(e){if(this.latestRequirement!==void 0){this.latestRequirement.text=e}}setNewReqRisk(e){if(this.latestRequirement!==void 0){this.latestRequirement.risk=e}}setNewReqVerifyMethod(e){if(this.latestRequirement!==void 0){this.latestRequirement.verifyMethod=e}}addElement(e){if(!this.elements.has(e)){this.elements.set(e,{name:e,type:this.latestElement.type,docRef:this.latestElement.docRef,cssStyles:[],classes:["default"]});y.Rm.info("Added new element: ",e)}this.resetLatestElement();return this.elements.get(e)}getElements(){return this.elements}setNewElementType(e){if(this.latestElement!==void 0){this.latestElement.type=e}}setNewElementDocRef(e){if(this.latestElement!==void 0){this.latestElement.docRef=e}}addRelationship(e,t,s){this.relations.push({type:e,src:t,dst:s})}getRelationships(){return this.relations}clear(){this.relations=[];this.resetLatestRequirement();this.requirements=new Map;this.resetLatestElement();this.elements=new Map;this.classes=new Map;(0,y.IU)()}setCssStyle(e,t){for(const s of e){const e=this.requirements.get(s)??this.elements.get(s);if(!t||!e){return}for(const s of t){if(s.includes(",")){e.cssStyles.push(...s.split(","))}else{e.cssStyles.push(s)}}}}setClass(e,t){for(const s of e){const e=this.requirements.get(s)??this.elements.get(s);if(e){for(const s of t){e.classes.push(s);const t=this.classes.get(s)?.styles;if(t){e.cssStyles.push(...t)}}}}}defineClass(e,t){for(const s of e){let e=this.classes.get(s);if(e===void 0){e={id:s,styles:[],textStyles:[]};this.classes.set(s,e)}if(t){t.forEach((function(t){if(/color/.exec(t)){const s=t.replace("fill","bgFill");e.textStyles.push(s)}e.styles.push(t)}))}this.requirements.forEach((e=>{if(e.classes.includes(s)){e.cssStyles.push(...t.flatMap((e=>e.split(","))))}}));this.elements.forEach((e=>{if(e.classes.includes(s)){e.cssStyles.push(...t.flatMap((e=>e.split(","))))}}))}}getClasses(){return this.classes}getData(){const e=(0,y.D7)();const t=[];const s=[];for(const i of this.requirements.values()){const s=i;s.id=i.name;s.cssStyles=i.cssStyles;s.cssClasses=i.classes.join(" ");s.shape="requirementBox";s.look=e.look;t.push(s)}for(const i of this.elements.values()){const s=i;s.shape="requirementBox";s.look=e.look;s.id=i.name;s.cssStyles=i.cssStyles;s.cssClasses=i.classes.join(" ");t.push(s)}for(const i of this.relations){let t=0;const r=i.type===this.Relationships.CONTAINS;const n={id:`${i.src}-${i.dst}-${t}`,start:this.requirements.get(i.src)?.name??this.elements.get(i.src)?.name,end:this.requirements.get(i.dst)?.name??this.elements.get(i.dst)?.name,label:`&lt;&lt;${i.type}&gt;&gt;`,classes:"relationshipLine",style:["fill:none",r?"":"stroke-dasharray: 10,7"],labelpos:"c",thickness:"normal",type:"normal",pattern:r?"normal":"dashed",arrowTypeStart:r?"requirement_contains":"",arrowTypeEnd:r?"":"requirement_arrow",look:e.look};s.push(n);t++}return{nodes:t,edges:s,other:{},config:e,direction:this.getDirection()}}};var d=(0,y.K2)((e=>`\n\n  marker {\n    fill: ${e.relationColor};\n    stroke: ${e.relationColor};\n  }\n\n  marker.cross {\n    stroke: ${e.lineColor};\n  }\n\n  svg {\n    font-family: ${e.fontFamily};\n    font-size: ${e.fontSize};\n  }\n\n  .reqBox {\n    fill: ${e.requirementBackground};\n    fill-opacity: 1.0;\n    stroke: ${e.requirementBorderColor};\n    stroke-width: ${e.requirementBorderSize};\n  }\n  \n  .reqTitle, .reqLabel{\n    fill:  ${e.requirementTextColor};\n  }\n  .reqLabelBox {\n    fill: ${e.relationLabelBackground};\n    fill-opacity: 1.0;\n  }\n\n  .req-title-line {\n    stroke: ${e.requirementBorderColor};\n    stroke-width: ${e.requirementBorderSize};\n  }\n  .relationshipLine {\n    stroke: ${e.relationColor};\n    stroke-width: 1;\n  }\n  .relationshipLabel {\n    fill: ${e.relationLabelColor};\n  }\n  .divider {\n    stroke: ${e.nodeBorder};\n    stroke-width: 1;\n  }\n  .label {\n    font-family: ${e.fontFamily};\n    color: ${e.nodeTextColor||e.textColor};\n  }\n  .label text,span {\n    fill: ${e.nodeTextColor||e.textColor};\n    color: ${e.nodeTextColor||e.textColor};\n  }\n  .labelBkg {\n    background-color: ${e.edgeLabelBackground};\n  }\n\n`),"getStyles");var E=d;var b={};(0,y.VA)(b,{draw:()=>R});var R=(0,y.K2)((async function(e,t,s,n){y.Rm.info("REF0:");y.Rm.info("Drawing requirement diagram (unified)",t);const{securityLevel:a,state:l,layout:c}=(0,y.D7)();const o=n.db.getData();const h=(0,i.A)(t,a);o.type=n.type;o.layoutAlgorithm=(0,r.q7)(c);o.nodeSpacing=l?.nodeSpacing??50;o.rankSpacing=l?.rankSpacing??50;o.markers=["requirement_contains","requirement_arrow"];o.diagramId=t;await(0,r.XX)(o,h);const f=8;u._K.insertTitle(h,"requirementDiagramTitleText",l?.titleTopMargin??25,n.db.getDiagramTitle());(0,i.P)(h,f,"requirementDiagram",l?.useMaxWidth??true)}),"draw");var k={parser:m,get db(){return new p},renderer:b,styles:E}}}]);