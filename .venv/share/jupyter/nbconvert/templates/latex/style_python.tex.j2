((= Python input/output style =))

((*- extends 'base.tex.j2' -*))

% Custom definitions
((* block definitions *))
    ((( super() )))

    % Pygments definitions
    ((( resources.latex.pygments_definitions )))
((* endblock definitions *))

%===============================================================================
% Input
%===============================================================================

((* block input scoped *))
    \begin{Verbatim}[commandchars=\\\{\}]
((*- if resources.global_content_filter.include_input_prompt *))
((( cell.source | highlight_code(strip_verbatim=True, metadata=cell.metadata) | add_prompts )))
((* else *))
((( cell.source | highlight_code(strip_verbatim=True, metadata=cell.metadata) )))
((* endif *))
    \end{Verbatim}
((* endblock input *))
