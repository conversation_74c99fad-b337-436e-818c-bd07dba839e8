#!/usr/bin/env python3
"""
Generate Xero-ready transaction reports for UCA Caboolture Region
Processes Square transaction data for May 2025
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os

def load_transaction_data():
    """Load and clean the main transaction data"""
    print("Loading transaction data...")
    
    # Load the main transactions file
    df = pd.read_csv('finance_downloads/transactions-2025-05-01-2025-06-01.csv')
    
    print(f"Loaded {len(df)} transactions")
    return df

def clean_and_categorize_data(df):
    """Clean data and categorize transactions for Xero"""
    print("Cleaning and categorizing data...")
    
    # Convert date to proper format
    df['Date'] = pd.to_datetime(df['Date'])
    
    # Clean amounts - remove $ and convert to float
    amount_cols = ['Net Sales', 'Gross Sales', 'Fees']
    for col in amount_cols:
        df[col] = df[col].str.replace('$', '').str.replace(',', '').astype(float)
    
    # Categorize transaction types
    def categorize_transaction(description):
        if pd.isna(description):
            return 'Donation'
        elif 'Offering' in description:
            return 'Offering'
        elif 'Craft' in description:
            return 'Sales'
        else:
            return 'Donation'
    
    df['Transaction_Type'] = df['Description'].apply(categorize_transaction)
    
    # Suggest account codes for Xero
    def suggest_account_code(trans_type):
        mapping = {
            'Offering': '4-1100',  # Church Offerings
            'Donation': '4-1200',  # General Donations  
            'Sales': '4-2000'      # Sales Revenue
        }
        return mapping.get(trans_type, '4-1200')
    
    df['Suggested_Account_Code'] = df['Transaction_Type'].apply(suggest_account_code)
    
    # Clean customer names
    df['Customer Name'] = df['Customer Name'].fillna('')
    
    # Clean payment methods
    df['Payment_Method'] = df['Card Brand'].fillna('Unknown')
    
    return df

def create_xero_report(df):
    """Create the main Xero import file"""
    print("Creating Xero import report...")
    
    # Select and rename columns for Xero
    xero_df = df[['Date', 'Time', 'Net Sales', 'Gross Sales', 'Description', 
                  'Payment_Method', 'Transaction ID', 'Customer Name', 'Fees',
                  'Suggested_Account_Code', 'Transaction_Type']].copy()
    
    # Rename columns for clarity
    xero_df.columns = ['Date', 'Time', 'Amount', 'Gross_Amount', 'Description', 
                       'Payment_Method', 'Reference', 'Customer_Name', 'Processing_Fees',
                       'Account_Code', 'Category']
    
    # Format date for Xero (YYYY-MM-DD)
    xero_df['Date'] = xero_df['Date'].dt.strftime('%Y-%m-%d')
    
    # Round amounts to 2 decimal places
    xero_df['Amount'] = xero_df['Amount'].round(2)
    xero_df['Gross_Amount'] = xero_df['Gross_Amount'].round(2)
    xero_df['Processing_Fees'] = xero_df['Processing_Fees'].round(2)
    
    # Sort by date and time
    xero_df = xero_df.sort_values(['Date', 'Time'])
    
    return xero_df

def create_council_report(df):
    """Create a summary report for church council presentation"""
    print("Creating council summary report...")
    
    # Create summary by transaction type
    summary = df.groupby('Transaction_Type').agg({
        'Net Sales': ['count', 'sum'],
        'Fees': 'sum'
    }).round(2)
    
    summary.columns = ['Count', 'Total_Amount', 'Total_Fees']
    summary = summary.reset_index()
    
    # Create daily summary
    daily_summary = df.groupby(df['Date'].dt.date).agg({
        'Net Sales': ['count', 'sum'],
        'Fees': 'sum'
    }).round(2)
    
    daily_summary.columns = ['Daily_Count', 'Daily_Amount', 'Daily_Fees']
    daily_summary = daily_summary.reset_index()
    
    # Create detailed council report
    council_df = df[['Date', 'Net Sales', 'Transaction_Type', 'Payment_Method', 
                     'Customer Name', 'Description']].copy()
    
    council_df.columns = ['Date', 'Amount', 'Type', 'Payment_Method', 
                          'Customer', 'Notes']
    
    # Format date for readability
    council_df['Date'] = council_df['Date'].dt.strftime('%Y-%m-%d')
    council_df['Amount'] = council_df['Amount'].round(2)
    
    # Sort by date
    council_df = council_df.sort_values('Date')
    
    return council_df, summary, daily_summary

def generate_summary_statistics(df):
    """Generate summary statistics for the reports"""
    print("Generating summary statistics...")
    
    total_transactions = len(df)
    total_amount = df['Net Sales'].sum()
    total_fees = df['Fees'].sum()
    net_amount = total_amount - total_fees
    
    # Payment method breakdown
    payment_summary = df.groupby('Payment_Method')['Net Sales'].agg(['count', 'sum']).round(2)
    
    # Transaction type breakdown
    type_summary = df.groupby('Transaction_Type')['Net Sales'].agg(['count', 'sum']).round(2)
    
    stats = {
        'total_transactions': total_transactions,
        'total_amount': round(total_amount, 2),
        'total_fees': round(total_fees, 2),
        'net_amount': round(net_amount, 2),
        'payment_summary': payment_summary,
        'type_summary': type_summary,
        'date_range': f"{df['Date'].min().strftime('%Y-%m-%d')} to {df['Date'].max().strftime('%Y-%m-%d')}"
    }
    
    return stats

def main():
    """Main function to generate all reports"""
    print("=== UCA Caboolture Region - Xero Transaction Report Generator ===")
    print("Processing May 2025 transactions...\n")
    
    try:
        # Load and process data
        df = load_transaction_data()
        df = clean_and_categorize_data(df)
        
        # Generate reports
        xero_df = create_xero_report(df)
        council_df, summary, daily_summary = create_council_report(df)
        stats = generate_summary_statistics(df)
        
        # Save reports
        print("\nSaving reports...")
        
        # Main Xero import file
        xero_df.to_csv('xero_transactions_may_2025.csv', index=False)
        print("✓ Saved: xero_transactions_may_2025.csv")
        
        # Council presentation file
        council_df.to_csv('council_report_may_2025.csv', index=False)
        print("✓ Saved: council_report_may_2025.csv")
        
        # Summary files
        summary.to_csv('transaction_summary_by_type.csv', index=False)
        daily_summary.to_csv('daily_summary_may_2025.csv', index=False)
        print("✓ Saved: transaction_summary_by_type.csv")
        print("✓ Saved: daily_summary_may_2025.csv")
        
        # Print summary statistics
        print(f"\n=== SUMMARY STATISTICS ===")
        print(f"Report Period: {stats['date_range']}")
        print(f"Total Transactions: {stats['total_transactions']}")
        print(f"Total Amount Collected: ${stats['total_amount']:,.2f}")
        print(f"Total Processing Fees: ${stats['total_fees']:,.2f}")
        print(f"Net Amount: ${stats['net_amount']:,.2f}")
        
        print(f"\n=== TRANSACTION TYPE BREAKDOWN ===")
        for trans_type, data in stats['type_summary'].iterrows():
            print(f"{trans_type}: {int(data['count'])} transactions, ${data['sum']:,.2f}")
        
        print(f"\n=== PAYMENT METHOD BREAKDOWN ===")
        for method, data in stats['payment_summary'].iterrows():
            print(f"{method}: {int(data['count'])} transactions, ${data['sum']:,.2f}")
        
        print(f"\n=== FILES READY FOR XERO IMPORT ===")
        print("1. xero_transactions_may_2025.csv - Main file for Xero import")
        print("2. council_report_may_2025.csv - Summary for church council")
        print("3. transaction_summary_by_type.csv - Category breakdown")
        print("4. daily_summary_may_2025.csv - Daily transaction summary")
        
        print(f"\n=== XERO IMPORT INSTRUCTIONS ===")
        print("1. In Xero, go to Accounting > Bank accounts")
        print("2. Select 'Import a statement'")
        print("3. Upload 'xero_transactions_may_2025.csv'")
        print("4. Map columns: Date, Amount, Description, Reference")
        print("5. Use suggested Account_Code for proper categorization")
        print("6. Review and reconcile transactions")
        
    except Exception as e:
        print(f"Error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
