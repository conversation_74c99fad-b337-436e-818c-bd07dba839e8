# Import required libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Set display options for better viewing
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 50)
pd.set_option('display.width', None)
pd.set_option('display.max_colwidth', 50)

print("✅ Libraries loaded successfully!")
print("📊 Ready for data manipulation")

# Load the ItemisedReport-May.csv file
file_path = 'Square_Reports/square - 5 may/ItemisedReport-May.csv'

try:
    # Load the data
    df = pd.read_csv(file_path)
    
    print(f"✅ Data loaded successfully!")
    print(f"📊 Shape: {df.shape[0]} rows × {df.shape[1]} columns")
    print(f"📅 Date range: {df['Date'].min()} to {df['Date'].max()}")
    
    # Display first few rows
    print("\n🔍 First 5 rows:")
    display(df.head())
    
except FileNotFoundError:
    print("❌ File not found. Please check the file path.")
except Exception as e:
    print(f"❌ Error loading file: {e}")

# Display data info and structure
print("📋 Data Information:")
print(df.info())

print("\n📊 Column Names:")
for i, col in enumerate(df.columns):
    print(f"{i}: {col}")

print("\n📈 Data Types:")
display(df.dtypes)

print("\n🔢 Basic Statistics:")
display(df.describe())

def edit_cell(row_index, column_name, new_value):
    """
    Edit a single cell in the dataframe
    
    Parameters:
    row_index: int - Row number (0-based)
    column_name: str - Column name
    new_value: any - New value for the cell
    """
    try:
        old_value = df.at[row_index, column_name]
        df.at[row_index, column_name] = new_value
        print(f"✅ Cell [{row_index}, '{column_name}'] changed from '{old_value}' to '{new_value}'")
        return True
    except Exception as e:
        print(f"❌ Error editing cell: {e}")
        return False

def edit_range(row_start, row_end, column_name, new_value):
    """
    Edit a range of cells in a column
    
    Parameters:
    row_start: int - Starting row (inclusive)
    row_end: int - Ending row (inclusive)
    column_name: str - Column name
    new_value: any - New value for all cells in range
    """
    try:
        df.loc[row_start:row_end, column_name] = new_value
        print(f"✅ Range [{row_start}:{row_end}, '{column_name}'] updated to '{new_value}'")
        return True
    except Exception as e:
        print(f"❌ Error editing range: {e}")
        return False

def view_rows(start_row, end_row=None):
    """
    View specific rows
    
    Parameters:
    start_row: int - Starting row
    end_row: int - Ending row (optional, defaults to start_row)
    """
    if end_row is None:
        end_row = start_row
    
    try:
        result = df.iloc[start_row:end_row+1]
        print(f"📋 Rows {start_row} to {end_row}:")
        display(result)
        return result
    except Exception as e:
        print(f"❌ Error viewing rows: {e}")
        return None

def search_data(column_name, search_value):
    """
    Search for specific values in a column
    
    Parameters:
    column_name: str - Column to search in
    search_value: any - Value to search for
    """
    try:
        if isinstance(search_value, str):
            # Case-insensitive string search
            mask = df[column_name].astype(str).str.contains(search_value, case=False, na=False)
        else:
            # Exact match for non-string values
            mask = df[column_name] == search_value
        
        results = df[mask]
        print(f"🔍 Found {len(results)} matches for '{search_value}' in '{column_name}':")
        display(results)
        return results
    except Exception as e:
        print(f"❌ Error searching: {e}")
        return None

print("✅ Editing functions loaded!")
print("\n📝 Available functions:")
print("• edit_cell(row_index, column_name, new_value)")
print("• edit_range(row_start, row_end, column_name, new_value)")
print("• view_rows(start_row, end_row)")
print("• search_data(column_name, search_value)")

# Remove empty rows (where Date is NaN or empty)
df_clean = df.dropna(subset=['Date'])
df_clean = df_clean[df_clean['Date'] != '']

print(f"📊 Clean data: {len(df_clean)} rows (removed {len(df) - len(df_clean)} empty rows)")

# Show unique transaction types
print("\n🏷️ Transaction Categories:")
if 'Category' in df_clean.columns:
    category_counts = df_clean['Category'].value_counts()
    display(category_counts)

# Show payment methods
print("\n💳 Payment Methods:")
if 'Payment_Method' in df_clean.columns:
    payment_counts = df_clean['Payment_Method'].value_counts()
    display(payment_counts)

# Show amount statistics
print("\n💰 Amount Statistics:")
if 'Amount' in df_clean.columns:
    amount_stats = df_clean['Amount'].describe()
    display(amount_stats)

# Example 1: View a specific row before editing
print("📋 Example: Viewing row 10")
view_rows(10)

# Example 2: Search for offering transactions
print("\n🔍 Example: Searching for 'Offering' transactions")
offering_results = search_data('Description', 'Offering')

# Example 3: Edit a cell (uncomment to use)
# edit_cell(10, 'Description', 'Updated Description')

# Example 4: Edit a range (uncomment to use)
# edit_range(5, 10, 'Category', 'Updated Category')

print("\n💡 To edit data, uncomment the edit_cell() or edit_range() lines above and run the cell")

def clean_amounts():
    """
    Clean amount columns by removing $ signs and converting to float
    """
    amount_columns = ['Amount', 'Gross_Amount', 'Processing_Fees']
    
    for col in amount_columns:
        if col in df.columns:
            # Remove $ signs and convert to float
            df[col] = df[col].astype(str).str.replace('$', '').str.replace(',', '')
            df[col] = pd.to_numeric(df[col], errors='coerce')
            print(f"✅ Cleaned {col} column")

def validate_data():
    """
    Validate data for common issues
    """
    print("🔍 Data Validation Report:")
    
    # Check for missing values
    missing_data = df.isnull().sum()
    if missing_data.sum() > 0:
        print("\n⚠️ Missing values found:")
        display(missing_data[missing_data > 0])
    else:
        print("✅ No missing values found")
    
    # Check for duplicate rows
    duplicates = df.duplicated().sum()
    if duplicates > 0:
        print(f"\n⚠️ Found {duplicates} duplicate rows")
    else:
        print("✅ No duplicate rows found")
    
    # Check for negative amounts (should only be in fees)
    if 'Amount' in df.columns:
        negative_amounts = (df['Amount'] < 0).sum()
        if negative_amounts > 0:
            print(f"\n⚠️ Found {negative_amounts} negative amounts")
        else:
            print("✅ No negative amounts in main Amount column")

# Run validation
validate_data()

# Uncomment to clean amounts
# clean_amounts()

def save_data(filename=None, backup=True):
    """
    Save the modified dataframe back to CSV
    
    Parameters:
    filename: str - Optional custom filename
    backup: bool - Whether to create a backup of original file
    """
    try:
        if filename is None:
            filename = file_path
        
        # Create backup if requested
        if backup and filename == file_path:
            backup_name = file_path.replace('.csv', '_backup.csv')
            import shutil
            shutil.copy2(file_path, backup_name)
            print(f"📁 Backup created: {backup_name}")
        
        # Save the data
        df.to_csv(filename, index=False)
        print(f"✅ Data saved to: {filename}")
        print(f"📊 Saved {len(df)} rows × {len(df.columns)} columns")
        
    except Exception as e:
        print(f"❌ Error saving file: {e}")

# Example usage (uncomment to save):
# save_data()  # Save to original file with backup
# save_data('ItemisedReport-May-Modified.csv', backup=False)  # Save to new file

print("💾 Save function ready!")
print("\n📝 Usage:")
print("• save_data() - Save to original file with backup")
print("• save_data('new_filename.csv') - Save to new file")
print("• save_data(backup=False) - Save without creating backup")

# Create visualizations of the transaction data
plt.style.use('default')
fig, axes = plt.subplots(2, 2, figsize=(15, 10))
fig.suptitle('UCA Caboolture Region - May 2025 Transaction Analysis', fontsize=16, fontweight='bold')

# Clean data for plotting
df_plot = df.dropna(subset=['Date'])
df_plot = df_plot[df_plot['Date'] != '']

if len(df_plot) > 0:
    # 1. Transaction Categories
    if 'Category' in df_plot.columns:
        category_counts = df_plot['Category'].value_counts()
        axes[0,0].pie(category_counts.values, labels=category_counts.index, autopct='%1.1f%%')
        axes[0,0].set_title('Transaction Categories')
    
    # 2. Payment Methods
    if 'Payment_Method' in df_plot.columns:
        payment_counts = df_plot['Payment_Method'].value_counts()
        axes[0,1].bar(payment_counts.index, payment_counts.values)
        axes[0,1].set_title('Payment Methods')
        axes[0,1].tick_params(axis='x', rotation=45)
    
    # 3. Amount Distribution
    if 'Amount' in df_plot.columns:
        # Convert amounts to numeric, handling any string values
        amounts = pd.to_numeric(df_plot['Amount'], errors='coerce')
        amounts = amounts.dropna()
        if len(amounts) > 0:
            axes[1,0].hist(amounts, bins=20, edgecolor='black', alpha=0.7)
            axes[1,0].set_title('Amount Distribution')
            axes[1,0].set_xlabel('Amount ($)')
            axes[1,0].set_ylabel('Frequency')
    
    # 4. Daily Transaction Count
    if 'Date' in df_plot.columns:
        try:
            df_plot['Date'] = pd.to_datetime(df_plot['Date'])
            daily_counts = df_plot['Date'].dt.date.value_counts().sort_index()
            axes[1,1].plot(daily_counts.index, daily_counts.values, marker='o')
            axes[1,1].set_title('Daily Transaction Count')
            axes[1,1].set_xlabel('Date')
            axes[1,1].set_ylabel('Number of Transactions')
            axes[1,1].tick_params(axis='x', rotation=45)
        except:
            axes[1,1].text(0.5, 0.5, 'Date format issue', ha='center', va='center')
            axes[1,1].set_title('Daily Transaction Count')

plt.tight_layout()
plt.show()

print("📊 Visualizations created!")